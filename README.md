# 活动服务 (Activity Service)

这是一个专门为"爆护之路"活动设计的Go微服务，采用了简洁的架构设计，既考虑了通用性又避免了过度设计。

## 项目结构

```
activitysrv/
├── cmd/                                    # 应用入口
│   └── main.go
├── internal/                               # 核心应用逻辑
│   ├── dao/                               # 数据访问层
│   │   └── dao_explosive_protection/      # 爆护活动数据访问
│   │       ├── cycle_dao.go              # 周期数据操作
│   │       └── user_data_dao.go          # 用户数据操作
│   ├── logic/                             # 业务逻辑层
│   │   ├── activity_base.go              # 活动基础框架
│   │   └── l_explosive_protection/       # 爆护活动逻辑
│   │       └── explosive_protection_logic.go
│   ├── model/                             # 数据模型
│   │   ├── activity_cycle.go             # 活动周期模型
│   │   ├── activity_progress.go          # 活动进度模型
│   │   ├── constants.go                  # 常量定义
│   │   └── user_activity_data.go         # 用户活动数据模型
│   ├── proc/                              # 处理器
│   │   └── activity_handler.go
│   ├── server/                            # RPC接口
│   │   └── activity_server.go
│   └── services/                          # 服务层
│       └── activity_service.go
├── test/                                   # 测试
│   └── activity_test.go
├── go.mod
├── go.sum
└── README.md
```

## 核心功能

### 1. 爆护之路活动

"爆护之路"是一个基于入护（钓鱼）数据的阶段性奖励活动：

- **事件处理**: 监听入护成功事件，更新玩家指标
- **进度查询**: 返回当前和上一周期的玩家数据
- **奖励领取**: 验证条件并发放阶段奖励
- **红点提醒**: 在登录和旅行结束时检查可领取奖励

### 2. 业务流程

#### 入护事件处理
1. 服务收到入护成功消息
2. 查询Activity表筛选有效活动
3. 查询或创建Redis周期数据
4. 更新玩家对应的指标值

#### 界面数据获取
1. 客户端请求活动进度
2. 返回上个周期（如有）和当前周期的数据
3. 包含玩家指标值和已领取阶段

#### 奖励领取
1. 客户端传入活动ID、周期ID、阶段ID
2. 校验活动时间（仅保留上一周期）
3. 验证领取条件和状态
4. 记录领取并调用大厅服发奖

#### 红点检查
1. 在登录后和旅行结束时触发
2. 检查是否有可领取的奖励
3. 返回红点状态

## 技术特点

### 1. 简洁的通用框架

采用了`ActivityHandler`接口设计，支持多种活动类型但避免过度抽象：

```go
type ActivityHandler interface {
    GetActivityId() int64
    HandleEvent(ctx context.Context, playerId uint64, event *commonPB.EventCommon) error
    GetProgress(ctx context.Context, playerId uint64) (interface{}, error)
    ClaimReward(ctx context.Context, playerId uint64, params map[string]interface{}) error
    CheckRedDot(ctx context.Context, playerId uint64) (bool, error)
}
```

### 2. Redis数据管理

- **周期管理**: `activity:{activity_id}` 存储周期信息
- **用户数据**: `act:{activity_id}:{player_id}:{cycle_id}` 存储玩家数据
- **TTL策略**: 当前周期剩余天数 + 周期天数 + 1 + 随机1天

### 3. 指标系统

支持多种指标类型：
- **累加类型**: 如总鱼重量、总鱼数量
- **最大值类型**: 如单次最大重量
- **扩展字段**: 支持JSON格式的复杂数据

### 4. 阶段配置

灵活的阶段配置系统：
```go
type StageConfig struct {
    StageId    int32           `json:"stage_id"`    // 阶段ID
    Conditions map[int32]int64 `json:"conditions"`  // 完成条件
    Rewards    []RewardItem    `json:"rewards"`     // 奖励列表
}
```

## 运行和测试

### 编译
```bash
go build ./cmd/main.go
```

### 运行测试
```bash
go test ./test/activity_test.go -v
```

### 依赖管理
```bash
go mod tidy
```

## 设计原则

1. **简洁优于复杂**: 避免过度设计，专注于当前需求
2. **可扩展性**: 通过接口设计支持新活动类型
3. **数据一致性**: 使用Redis事务保证数据原子性
4. **错误处理**: 完善的错误处理和日志记录
5. **测试覆盖**: 核心逻辑都有对应的单元测试

## 未来扩展

当需要添加新的活动类型时，只需要：

1. 实现`ActivityHandler`接口
2. 在`ActivityManager`中注册新的处理器
3. 添加对应的数据模型和DAO

这种设计既保持了代码的简洁性，又为未来的扩展提供了良好的基础。

活动服务，负责处理游戏中的各种活动系统。

## 功能特性

- 接收事件更新玩家活动数据
- 支持多种活动类型的抽象处理
- 活动进度跟踪和奖励发放
- 活动状态管理

## 架构设计

```mermaid
mindmap
root((activitysrv))
    事件处理
        登录事件
        游戏行为事件
        活动相关事件
    活动管理
        活动类型抽象
        活动进度更新
        活动状态管理
    奖励发放
        活动奖励
        进度奖励
        特殊奖励
    数据管理
        缓存层(Redis)
        持久化层(MySQL)
        数据一致性
```

## 目录结构

- `cmd/` - 应用程序入口
- `config/` - 配置相关
- `internal/` - 核心应用逻辑
  - `dao/` - 数据访问层
  - `logic/` - 业务逻辑层
  - `model/` - 数据模型
  - `services/` - 服务层
  - `pubsub/` - 消息订阅
  - `rpc/` - RPC服务实现 