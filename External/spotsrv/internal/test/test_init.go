package test_init

import (
	"context"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_redis"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/nsqx"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"git.keepfancy.xyz/back-end/frameworks/lib/logdog"
	"github.com/spf13/viper"
)

func InitRedisConsul() {
	viper.SetDefault("consul_addr", "************:8500")
	viper.SetDefault("kafka_url", "************:9092")

	conf := map[string]string{
		"addr":   "************:6379",
		"passwd": "8888",
	}
	viper.SetDefault("redis_list", map[string]interface{}{
		dict_redis.RDBGame:   conf,
		dict_redis.RDBPlayer: conf,
	})
}

func Init() {
	logdog.SetupLog("debug", false)

	serverHost := "************"
	// serverHost := "localhost"
	viper.SetDefault(dict.ConfigConsulAddr, serverHost+":8500")
	viper.SetDefault(dict.ConfigKafkaUrl, serverHost+":9092")
	viper.SetDefault(dict.ConfigNsqDAddr, serverHost+":4150")

	// redis
	viper.SetDefault(dict.ConfigRedisAddr, serverHost+":6379")
	viper.SetDefault(dict.ConfigRedisPwd, "8888")

	rdsConf := map[string]string{
		"addr":   serverHost + ":6379",
		"passwd": "8888",
	}

	redisMap := map[string]interface{}{
		dict_redis.RDBGame:    rdsConf,
		dict_redis.RDBPlayer:  rdsConf,
		dict_redis.RDBGeneral: rdsConf,
		dict_redis.RDBLock:    rdsConf,
	}

	viper.SetDefault(dict.ConfigRedisList, redisMap)

	// nsq
	viper.SetDefault(dict.ConfigNsqDAddr, serverHost+":4150")
	viper.SetDefault(dict.ConfigNsqHttpAddr, serverHost+":4151")
	viper.SetDefault(dict.ConfigNsqLookUpdAddress, serverHost+":4161")
	nsqx.Setup()
}

func NewCtxWithPlayerId(playerId uint64) context.Context {
	ctx := interceptor.NewRpcClientCtx(
		interceptor.WithProductId(1),
		interceptor.WithChannelType(1001),
		interceptor.WithCountry("zh-cn"),
		interceptor.WithPlayerId(playerId),
	)
	return ctx
}
