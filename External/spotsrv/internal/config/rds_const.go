package config

import (
	"fmt"
	"time"
)

// redis key
const (
	// string
	// 玩家钓上来的鱼信息(未入户)
	RDS_KEY_HOOK_FISH = "p_hook_fish:%d" //playerId
	
	// hash
	// 玩家旅行经验数据(离开房间展示的数据 经验已经被加到玩家身上)
	RDS_KEY_TRAVEL_EXP = "p_travel_exp:%d" //playerId
	// 玩家鱼护信息(已入护)
	RDS_KEY_FISH_KEEPNET = "p_fish_keepnet:%d" //playerId
	// 钓点玩家数据
	RDS_KEY_PLAYER_SPOT_INFO = "p_spot_info:%d" //playerId
	// 玩家上次游戏信息(hall服务同步使用 不可随意修改)
	RDS_KEY_PLAYER_LAST_GAME_INFO = "p_last_game:%d"

	// 上次游戏信息 spot filed
	LastGameInfoSpot = "spot_id"

	// zset
	RDS_KEEPNET_PLAYER = "keepnet_player"
)

// lock
const (
	RDS_LOCK_KEEPNET_CORN = "lock:keepnet:cron"
)

// redis expire
const (
	// 玩家调上来的鱼信息过期时间
	HOOK_FISH_EXPIRE = 7 * 24 * time.Hour
	// 玩家旅行经验过期时间
	TRAVEL_EXP_EXPIRE = 7 * 24 * time.Hour
	// 钓点玩家数据过期时间
	PLAYER_SPOT_INFO_EXPIRE = 7 * 24 * time.Hour
	// 玩家上次游戏信息过期时间
	PLAYER_LAST_GAME_EXPIRE = 7 * 24 * time.Hour
	// 玩家鱼护信息过期时间 15天
	FISH_KEEPNET_EXPIRE = 15 * 24 * time.Hour
)

// FmtPlayerHookFishRdsKey 格式化玩家钓上的鱼保存信息key
func FmtPlayerHookFishRdsKey(playerId uint64) string {
	return fmt.Sprintf(RDS_KEY_HOOK_FISH, playerId)
}

// FmtPlayerTravelExpRdsKey 格式化玩家旅游经验值key hash
func FmtPlayerTravelExpRdsKey(playerId uint64) string {
	return fmt.Sprintf(RDS_KEY_TRAVEL_EXP, playerId)
}

// FmtPlayerFishKeepnetRdsKey 格式化玩家鱼护信息key
func FmtPlayerFishKeepnetRdsKey(playerId uint64) string {
	return fmt.Sprintf(RDS_KEY_FISH_KEEPNET, playerId)
}

// FmtSpotPlayerRdsKey 格式化钓点玩家 key hash
func FmtSpotPlayerRdsKey(playerId uint64) string {
	return fmt.Sprintf(RDS_KEY_PLAYER_SPOT_INFO, playerId)
}

// FmtPlayerLastGameRdsKey 格式化玩家上次游戏信息key
func FmtPlayerLastGameRdsKey(playerId uint64) string {
	return fmt.Sprintf(RDS_KEY_PLAYER_LAST_GAME_INFO, playerId)
}

