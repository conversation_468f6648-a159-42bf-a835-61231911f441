package config

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/config"
)

// InitConfig 初始化配置
func InitConfig() error {
	serviceConfig := config.NewServiceConfig()

	serviceConfig.Register("InitBasicFishConstCfg", cmodel.InitBasicFishConstCfg)              // 鱼基础const配置表
	serviceConfig.Register("InitBasicFishQualityCfg", cmodel.InitBasicFishQualityCfg)          // 鱼详情配置表
	serviceConfig.Register("InitFishPondListCfg", cmodel.InitFishPondListCfg)                  // 钓场配置
	serviceConfig.Register("InitRoleConstCfg", cmodel.InitRoleConstCfg)                        // 体力消耗配置
	serviceConfig.Register("InitRoleLevelCfg", cmodel.InitRoleLevelCfg)                        // 经验等级配置
	serviceConfig.Register("InitBroadcastTempCfg", cmodel.InitBroadcastTempCfg)                // 播报模板配置
	serviceConfig.Register("InitTackleDurabilityCostCfg", cmodel.InitTackleDurabilityCostCfg)  // 耐久度计算
	serviceConfig.Register("InitBobbersCfg", cmodel.InitBobbersCfg)                            // 浮漂配置
	serviceConfig.Register("InitItemCfg", cmodel.InitItemCfg)                                  // 道具配置
	serviceConfig.Register("InitEventTempMapCfg", cmodel.InitEventTempMapCfg)                  // 播报配置
	serviceConfig.Register("InitBasicFishSpeciesCfg", cmodel.InitBasicFishSpeciesCfg)          // 鱼种配置
	serviceConfig.Register("InitStatsCfg", cmodel.InitStatsCfg)                                // 统计配置

	return serviceConfig.ExecuteAll()
}