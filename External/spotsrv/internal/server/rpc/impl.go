package rpc

import (
	"context"
	"spotsrv/internal/services"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	spotRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/spotrpc"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc"
)

type SpotRpcServer struct {
}

func InitSpotRpc() {
	spotRpcService := &SpotRpcServer{}
	spotRpc.RegisterSpotServiceServer(rpc.Server, spotRpcService)
}

func (s *SpotRpcServer) PondChangeEventNotify(ctx context.Context, req *spotRpc.PondChangeEventNtf) (*commonPB.Result, error) {
	return services.GetSpotServiceInstance().PondChangeEventNotify(ctx, req)
}