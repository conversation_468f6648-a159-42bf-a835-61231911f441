package modelKeepnet

import (
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/sirupsen/logrus"
)

type TravelExp struct {
	FishExp  int32 `json:"fish_exp"`  // 鱼货经验
	TotalExp int32 `json:"total_exp"` // 全部经验
}

// NewTravelExpRdsHash 从redis hash中解析出结构体
func NewTravelExpRdsHash(hash map[string]string) *TravelExp {
	travelExp := &TravelExp{}

	// 遍历hash填充结构体字段
	for key, value := range hash {
		switch key {
		case "fish_exp":
			travelExp.FishExp = transform.Str2Int32(value)
		case "total_exp":
			travelExp.TotalExp = transform.Str2Int32(value)
		default:
			logrus.Warnf("hash:%v, filed:%s, not find", hash, key)
		}
	}

	return travelExp
}

// ToRedisHashField 转化为redis field
func (t *TravelExp)ToRedisHashField() map[string]interface{} {
	return map[string]interface{}{
		"fish_exp":  t.FishExp,
		"total_exp": t.TotalExp,
	}
}