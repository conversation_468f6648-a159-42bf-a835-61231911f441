package modelKeepnet

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
)

const (
	FISH_MAX_FRESHNESS = 100 // 最大新鲜度
)

// 钓上来的鱼结构
type FishInfo struct {
	FishId   int64 `json:"fish_id"` // 鱼ID
	Length   int32 `json:"length"`  // 鱼身长度
	Weight   int32 `json:"weight"`  // 鱼体重
	Exp      int32 `json:"exp"`     // 鱼经验值
	AwardNum int32 `json:"-"`       // 奖励数量
}

// NewFishInfoFromPb 初始化鱼信息
func NewFishInfoFromPb(pb *commonPB.FishInfo) *FishInfo {
	if pb == nil {
		return nil
	}

	return &FishInfo{
		FishId:   pb.FishId,
		Length:   pb.Length,
		Weight:   pb.Weight,
		Exp:      pb.Exp,
		AwardNum: pb.AwardNum,
	}
}

// ToProto 转化为commPb proto
func (f *FishInfo) ToProto(ctx context.Context) *commonPB.FishInfo {
	pb := &commonPB.FishInfo{}
	pb.FishId = f.FishId
	pb.Length = f.Length
	pb.Weight = f.Weight
	pb.Exp = f.Exp
	pb.AwardNum = f.AwardNum

	fishCfg := cmodel.GetBasicFishQuality(f.FishId, consulconfig.WithGrpcCtx(ctx))
	if fishCfg != nil {
		pb.Special = int64(fishCfg.Species)
		pb.Somatotype = fishCfg.SizeCategory

		specilCfg := cmodel.GetBasicFishSpecies(pb.Special, consulconfig.WithGrpcCtx(ctx))
		if specilCfg != nil {
			pb.Genus = int32(specilCfg.Genus)
		}
	}
	return pb
}

// 鱼详细信息
type FishDetailInfo struct {
	FishInstance    string                       `json:"fish_instance"`     // 鱼实例
	Freshness       int32                        `json:"-"`                 // 鱼新鲜度
	HookTime        int64                        `json:"hook_time"`         // 钓上来的时间(时间戳)
	FishInfo        *FishInfo                    `json:"fish_info"`         // 鱼信息
	AwardPer        float32                      `json:"-"`                 // 奖励百分比
	IsFirst         bool                         `json:"-"`                 // 是否是第一次钓到
	OptType         commonPB.FISH_ENTRY_OPT_TYPE `json:"opt_type"`          // 鱼入护操作类型
	BaitId          int64                        `json:"bait_id"`           // 钓饵id
	FishDamagedInfo *commonPB.FishDamagedInfo    `json:"fish_damaged_info"` // 鱼破损信息
}

// NewFishDetailFromPb 初始化鱼详细信息
func NewFishDetailFromPb(ctx context.Context, pb *commonPB.FishInfo, damagedInfo *commonPB.FishDamagedInfo) *FishDetailInfo {
	// 创建一个FishDetailInfo结构体实例
	fishDetail := &FishDetailInfo{
		FishInfo:        NewFishInfoFromPb(pb),
		Freshness:       FISH_MAX_FRESHNESS,
		HookTime:        timex.Now().Unix(),
		FishInstance:    random.GetUUID(),
		FishDamagedInfo: damagedInfo,
	}
	fishDetail.CalcData(ctx)
	return fishDetail
}

// ToProto 转化为commPb proto
func (f *FishDetailInfo) ToProto(ctx context.Context) *commonPB.FishDetailInfo {
	if f.FishInfo == nil {
		return nil
	}

	pb := &commonPB.FishDetailInfo{}
	pb.FishInfo = f.FishInfo.ToProto(ctx)
	pb.Freshness = f.Freshness
	pb.HookTime = f.HookTime
	pb.InstanceId = f.FishInstance
	pb.AwardPer = f.AwardPer
	pb.IsFirst = f.IsFirst
	pb.BaitId = f.BaitId
	pb.FishDamagedInfo = f.FishDamagedInfo

	return pb
}

// CalcData 计算数据
func (f *FishDetailInfo) CalcData(ctx context.Context) error {
	// 根据配置 赋值新的数据
	entry := logx.NewLogEntry(ctx)
	fishConstConf := cmodel.GetBasicFishConst(consulconfig.WithGrpcCtx(ctx))
	if fishConstConf == nil {
		entry.Errorf("GetBasicFishConst is null")
		return errors.New("GetBasicFishConst is null")
	}

	// 计算新鲜度(间隔时间分钟递减)
	// 没分钟减少FishFreshnessDecay 百分比新鲜度
	minutesSinceHook := int32(time.Now().Unix()-f.HookTime) / 60
	f.Freshness = int32(FISH_MAX_FRESHNESS) - minutesSinceHook*fishConstConf.FishFreshnessDecay
	if f.Freshness < 0 {
		f.Freshness = 0
	}

	damagedValue := int32(100)
	if f.FishDamagedInfo != nil && f.FishDamagedInfo.GetFishDamagedValue() > 0 && f.FishDamagedInfo.GetFishDamagedValue() <= 100 {
		damagedValue = f.FishDamagedInfo.GetFishDamagedValue()
	}

	// 计算奖励百分比 奖励衰减值 = 新鲜度衰减值*fishConstConf.FishPriceDecay * / 10 * 破损度
	f.AwardPer = float32(FISH_MAX_FRESHNESS) - float32(FISH_MAX_FRESHNESS-f.Freshness)*float32(fishConstConf.FishPriceDecay)/10

	if f.AwardPer < 0 {
		f.AwardPer = 0
	}

	// 将破损度百分比 转化为 奖励百分比
	f.AwardPer = f.AwardPer * float32(damagedValue) / 100

	// 保价
	if fishConstConf.FishInsuredPer > 0 && f.AwardPer < float32(fishConstConf.FishInsuredPer) {
		f.AwardPer = float32(fishConstConf.FishInsuredPer)
	}

	// 查询鱼基础表
	fishBaseConf := cmodel.GetBasicFishQuality(int64(f.FishInfo.FishId), consulconfig.WithGrpcCtx(ctx))
	if fishBaseConf != nil {
		// 价格和经验使用重量(KG)计算 向上取整
		f.FishInfo.AwardNum = int32(f.AwardPer*float32(fishBaseConf.BasicRewardNum*f.FishInfo.Weight)/100/1000 + 1)
		f.FishInfo.Exp = int32(f.FishInfo.Weight*fishBaseConf.Exp/1000 + 1)
	}

	return nil
}

// ToJsonStr 转化为json字符串
func (f *FishDetailInfo) ToJsonStr() string {
	jsonStr, err := json.Marshal(f)
	if err != nil {
		return ""
	}

	return string(jsonStr)
}

// InitFromJson 初始化鱼详细信息
func (f *FishDetailInfo) InitFromJson(jsonStr string) error {
	err := json.Unmarshal([]byte(jsonStr), f)
	if err != nil {
		return err
	}

	return nil
}

// ToItemBase 转化为道具基础信息 用于发奖
func (f *FishDetailInfo) ToItemBase(ctx context.Context) []*commonPB.ItemBase {
	entry := logx.NewLogEntry(ctx)
	if f == nil {
		return nil
	}

	f.CalcData(ctx)

	// 查询鱼基础表
	fishBaseConf := cmodel.GetBasicFishQuality(int64(f.FishInfo.FishId), consulconfig.WithGrpcCtx(ctx))
	if fishBaseConf == nil {
		entry.Errorf("get fish:%d BasicFishQuality conf error", f.FishInfo.FishId)
		return nil
	}

	if fishBaseConf.BasicRewardItem <= 0 || fishBaseConf.BasicRewardNum <= 0 {
		entry.Warnf("get fish:%d BasicFishQuality conf:%v award info error", f.FishInfo.FishId, *fishBaseConf)
		return nil
	}

	itemList := make([]*commonPB.ItemBase, 0)
	// 基础奖励
	itemBase := &commonPB.ItemBase{
		ItemId:    fishBaseConf.BasicRewardItem,
		ItemCount: int64(f.FishInfo.AwardNum),
	}

	itemList = append(itemList, itemBase)

	// 额外奖励
	if fishBaseConf.ExtraGiftId > 0 {
		itemBase = &commonPB.ItemBase{
			ItemId:    fishBaseConf.ExtraGiftId,
			ItemCount: 1,
		}
		itemList = append(itemList, itemBase)
	}
	return itemList
}

// StatsTotalExp 统计总经验
func (f *FishDetailInfo) StatsTotalExp(ctx context.Context) int32 {
	if f == nil {
		return 0
	}
	f.CalcData(ctx)
	return f.FishInfo.Exp
}

// ToEventMap 转化为eventMap
func ToEventMap(f *commonPB.FishInfo, pondId int64, weather int64) map[int32]int64 {
	eventData := make(map[int32]int64)
	eventData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_ID)] = f.FishId
	eventData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_POND)] = pondId
	eventData[int32(commonPB.EVENT_INT_KEY_EIK_POND_ID)] = pondId
	eventData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_SPECIAL)] = f.Special
	eventData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_LENGTH)] = int64(f.Length)
	eventData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_WEIGHT)] = int64(f.Weight)
	eventData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_GENUS)] = int64(f.Genus)
	eventData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_SOMATOTYPE)] = int64(f.Somatotype)
	eventData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_QUALITY)] = int64(f.GetQuality())
	eventData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_WEATHER)] = weather
	eventData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_NUM)] = int64(1)
	return eventData
}
