package modelKeepnet

import (
	"context"
	"fmt"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
)

// 鱼护相关结构 redis
type FishKeepnet struct {
	TotalWeight int32                      `json:"total_weight"` // 鱼护中鱼的总重量(g)
	FishList    map[string]*FishDetailInfo `json:"fish_list"`    // 鱼护中鱼的信息
}

// NewFishKeepnetFromRedisHash 从redis map中解析出FishKeepnet
func NewFishKeepnetFromRedisHash(ctx context.Context, mapKeepnet map[string]*FishDetailInfo) *FishKeepnet {
	if len(mapKeepnet) == 0 {
		return &FishKeepnet{
			FishList: make(map[string]*FishDetailInfo, 0),
		}
	}

	fishKeepnet := &FishKeepnet{
		FishList: make(map[string]*FishDetailInfo, len(mapKeepnet)),
	}

	fishKeepnet.TotalWeight = 0
	for fishInstance, fishDetail := range mapKeepnet {
		// 入护标记的才记总重量
		if fishDetail.OptType == commonPB.FISH_ENTRY_OPT_TYPE_FEOT_KEEP {
			fishKeepnet.TotalWeight += fishDetail.FishInfo.Weight
		}
		fishDetail.CalcData(ctx)
		fishKeepnet.FishList[fishInstance] = fishDetail
	}

	return fishKeepnet
}

// ToRedisHash 转化为redis hash
func (f *FishKeepnet) ToRedisHash() map[string]interface{} {
	if f == nil {
		return nil
	}

	mapKeepnet := make(map[string]interface{}, len(f.FishList))

	for fishInstance, fishInfo := range f.FishList {
		mapKeepnet[fishInstance] = fishInfo.ToJsonStr()
	}

	return mapKeepnet
}

// AddFish 添加鱼
func (f *FishKeepnet) AddFish(ctx context.Context, fishDetail *FishDetailInfo) error {
	entry := logx.NewLogEntry(ctx)
	if f == nil || fishDetail == nil {
		entry.Errorf("fish keepnet is nil or fishDetail is nil")
		return fmt.Errorf("fish keepnet is nil or fishDetail is nil")
	}

	_, ok := f.FishList[fishDetail.FishInstance]
	if ok {
		entry.Errorf("fish %s is already in fish keepnet", fishDetail.FishInstance)
		return fmt.Errorf("fish %s is already in fish keepnet", fishDetail.FishInstance)
	} else {
		fishDetail.CalcData(ctx)
		f.FishList[fishDetail.FishInstance] = fishDetail
		f.TotalWeight += fishDetail.FishInfo.Weight
		fishDetail.HookTime = timex.Now().Unix() //添加鱼的时候 从新修改入护时间
	}

	return nil
}

// DiscardFish 丢弃鱼
func (f *FishKeepnet) DiscardFish(fishInstance string) error {
	if f == nil {
		return fmt.Errorf("fish keepnet is nil")
	}

	fishInfo, ok := f.FishList[fishInstance]
	if !ok {
		return fmt.Errorf("fish %s is not in fish keepnet", fishInstance)
	} else {
		f.TotalWeight -= fishInfo.FishInfo.Weight
		// 标记为放生
		fishInfo.OptType = commonPB.FISH_ENTRY_OPT_TYPE_FEOT_RELEASE
	}

	return nil
}
