package subscribe

import (
	"context"

	"spotsrv/internal/pubsub/publish"
	rpcTrip "spotsrv/internal/repo/rpc_trip"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"github.com/sirupsen/logrus"
)

type OfflineEvent struct {
}

func (o *OfflineEvent) HandlerEvent(msg *commonPB.EventCommon) {
	if msg == nil {
		return
	}
	
	intMap := msg.GetIntData()
	if len(intMap) <= 0 {
		logrus.Warnf("Handler offline: Empty Data, msg:%+v", msg)
		return
	}

	playerId := msg.GetPlayerId()
	//productId := msg.GetProductId()
	
	// 玩家下线处理
	// TODO　广播给房间所有玩家

	ctx := context.Background()

	// 玩家是否在房间中
	roomId, err :=  rpcTrip.GetPlayerRoomId(ctx, playerId)
	if err != nil || roomId == "" {
		logrus.Debugf("Handler offline: Player not in room, msg:%+v", msg)
		return
	}
	
	err = rpcTrip.OfflineExitTripRoom(ctx, playerId)
	if err != nil {
		logrus.Warnf("Handler offline, msg:%+v, warn:%+v", msg, err)
		return 
	}

	// 玩家离线回删除房间中玩家 这里处理成功后需要广播给sync服
	publish.PublishSpotLeaveSpot(ctx, playerId, roomId)
}