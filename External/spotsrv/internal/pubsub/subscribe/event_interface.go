package subscribe

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/proto"
)

// CommonEvent 通用事件处理接口
func CommonEvent(body []byte) {
	msg := &commonPB.EventCommon{}
	if err := proto.Unmarshal(body, msg); err != nil {
		logrus.Errorf("Handler Login Event:%s Error: %v", string(body), err)
		return
	}

	logrus.Debugf("Handler Login Event: %v", msg.String())

	// 处理当前数据
	RecvEvent(msg.EventType, msg)	
}

type IEvent interface {
	HandlerEvent(msg *commonPB.EventCommon)
}

func RecvEvent(eventType commonPB.EVENT_TYPE, msg *commonPB.EventCommon) {
	if msg == nil {
		return
	}

	var handler IEvent
	switch eventType {
	case commonPB.EVENT_TYPE_ET_LOGOUT:
		handler = new(OfflineEvent)
	default:
		return
	}

	handler.HandlerEvent(msg)
}
