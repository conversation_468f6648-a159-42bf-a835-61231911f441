package daoKeep

import (
	"context"

	"spotsrv/internal/config"
	modelKeepnet "spotsrv/internal/model/model_keepnet"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
)

// SetPlayerHookFish 保存玩家钓上的鱼(入护之前)
func SetPlayerHookFish(ctx context.Context, playerId uint64, fishInfo *modelKeepnet.FishDetailInfo) error {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 || fishInfo == nil {
		entry.Errorf("SetPlayerHookFish playerId:%d, fishInfo:%v", playerId, fishInfo)
		return nil
	}

	err := redisx.GetGameCli().Set(ctx, config.FmtPlayerHookFishRdsKey(playerId), fishInfo.ToJsonStr(), config.HOOK_FISH_EXPIRE).Err()

	return err
}

// GetPlayerHookFish 查询玩家钓上的鱼(入护之前)
func GetPlayerHookFish(ctx context.Context, playerId uint64) (*modelKeepnet.FishDetailInfo, error) {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 {
		entry.Errorf("GetPlayerHookFish playerId:%d", playerId)
		return nil, nil
	}
	str, err := redisx.GetGameCli().Get(ctx, config.FmtPlayerHookFishRdsKey(playerId)).Result()

	if err != nil {
		entry.Errorf("GetPlayerHookFish playerId:%d, str:%s, err:%v", playerId, str, err)
		return nil, err
	}

	fishDetail := &modelKeepnet.FishDetailInfo{}
	err = fishDetail.InitFromJson(str)
	if err != nil {
		entry.Errorf("GetPlayerHookFish playerId:%d, str:%s, err:%v", playerId, str, err)
		return nil, err
	}

	return fishDetail, nil
}

// DelPlayerHookFish 删除玩家钓上的鱼
func DelPlayerHookFish(ctx context.Context, playerId uint64) error {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 {
		entry.Errorf("DelPlayerHookFish playerId:%d", playerId)
		return nil
	}

	err := redisx.GetGameCli().Del(ctx, config.FmtPlayerHookFishRdsKey(playerId)).Err()
	return err
}
