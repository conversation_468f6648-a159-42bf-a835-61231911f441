package daoPlayer

import (
	"context"
	"errors"
	"spotsrv/internal/config"
	modelPlayer "spotsrv/internal/model/model_player"
	rpcTrip "spotsrv/internal/repo/rpc_trip"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
)

// QuerySpotPlayerInfo 查询玩家钓点信息
func QuerySpotPlayerInfo(ctx context.Context, playerId uint64) (*modelPlayer.PondPlayer, error) {
	entry := logx.NewLogEntry(ctx)
	// 查询玩家钓点信息
	playerHashMap, err := redisx.GetGameCli().HGetAll(ctx, config.FmtSpotPlayerRdsKey(playerId)).Result()

	if errors.Is(err, redis.Nil) || len(playerHashMap) <= 0 {
		return nil, nil
	}

	if err != nil {
		entry.Errorf("query player:%d, spot info err:%v", playerId, err)
		return nil, err
	}

	playerInfo := modelPlayer.NewPlayerInfoFormRdsField(playerHashMap)
	if playerInfo == nil {
		entry.Errorf("player:%d, spot info is error, player str:%v", playerId, playerInfo)
		return nil, errors.New("player info is empty")
	}

	return playerInfo, nil
}

// UpdateSpotPlayerRds 更新玩家钓点数据
func UpdateSpotPlayerRds(ctx context.Context, playerId uint64, updMap map[string]interface{}) error {
	entry := logx.NewLogEntry(ctx)
	if playerId <= 0 || len(updMap) <= 0 {
		entry.Errorf("update player:%d data:%v, param error", playerId, updMap)
		return errors.New("data or player info is empty")
	}

	// 加上过期时间
	pipLine := redisx.GetGameCli().TxPipeline()

	pipLine.HMSet(ctx, config.FmtSpotPlayerRdsKey(playerId), updMap).Result()
	pipLine.Expire(ctx, config.FmtSpotPlayerRdsKey(playerId), config.PLAYER_SPOT_INFO_EXPIRE).Result()

	_, err := pipLine.Exec(ctx)
	if err != nil {
		entry.Errorf("update player:%d, data:%v info err:%v", playerId, updMap, err)
	}

	return err
}

// DelSpotPlayerRds 删除钓点玩家数据
func DelSpotPlayerRds(ctx context.Context, playerId uint64) error {
	entry := logx.NewLogEntry(ctx)
	// 玩家退出房间
	_, err := redisx.GetGameCli().Del(ctx, config.FmtSpotPlayerRdsKey(playerId)).Result()
	if err != nil {
		entry.Errorf("del player:%d, spot info err:%v", playerId, err)
	}

	return err
}

// GetRoomAllPlayerInfoRds 查询房间所有玩家信息
func GetRoomAllPlayerInfoRds(ctx context.Context, roomId string) (map[uint64]*modelPlayer.PondPlayer, error) {
	entry := logx.NewLogEntry(ctx)
	if roomId == "" {
		entry.Errorf("query room all player info, room id is empty")
		return nil, errors.New("room id is empty")
	}

	// 先查询玩家列表
	playerList, err := rpcTrip.GetRoomPlayerList(ctx, roomId)
	if err != nil {
		entry.Errorf("query room all player info, room id:%s, err:%v", roomId, err)
		return nil, err
	}

	pipLine := redisx.GetGameCli().TxPipeline()
	for _, player := range playerList {
		pipLine.HGetAll(ctx, config.FmtSpotPlayerRdsKey(player))
	}

	playerMap := make(map[uint64]*modelPlayer.PondPlayer)

	// 解析结果
	cmds, err := pipLine.Exec(ctx)
	if err != nil {
		entry.Errorf("query room all player info, room id:%s, err:%v", roomId, err)
		return nil, err
	}

	for i, cmd := range cmds {
		playerId := playerList[i]
		playerHashMap, err := cmd.(*redis.StringStringMapCmd).Result()
		if errors.Is(err, redis.Nil) {
			continue
		}
		if err != nil {
			entry.Errorf("query room all player info, room id:%s, err:%v", roomId, err)
			return nil, err
		}
		playerInfo := modelPlayer.NewPlayerInfoFormRdsField(playerHashMap)
		if playerInfo == nil {
			entry.Errorf("query room all player info, room id:%s, err:playerInfo:%v is empty", roomId, playerHashMap)
			continue
		}
		playerMap[playerId] = playerInfo
	}

	return playerMap, nil
}

// UpdatePlayerLastGameSpotId 更新玩家上次游戏信息中的钓点信息
func UpdatePlayerLastGameSpotId(ctx context.Context, playerId uint64, spotId int32) error {
	entry := logx.NewLogEntry(ctx)
	if playerId <= 0 || spotId <= 0 {
		entry.Errorf("UpdatePlayerLastGameSpotId, playerId:%d, spotId:%d", playerId, spotId)
		return errors.New("playerId or spotId is empty")
	}

	// 使用TxPipeline 设置后加上过期时间
	pipLine := redisx.GetGameCli().TxPipeline()

	// 使用TxPipeline 设置后加上过期时间
	pipLine.HSet(ctx, config.FmtPlayerLastGameRdsKey(playerId), config.LastGameInfoSpot, spotId)
	pipLine.Expire(ctx, config.FmtPlayerLastGameRdsKey(playerId), config.PLAYER_LAST_GAME_EXPIRE)

	_, err := pipLine.Exec(ctx)
	if err != nil {
		entry.Errorf("update player:%d last game info spotId:%d, error: %v", playerId, spotId, err)
		return err
	}

	return err
}

// BatchGetSpotPlayerRds 批量查询玩家钓点信息
func BatchGetSpotPlayerRds(ctx context.Context, playerIds []uint64) map[uint64]*modelPlayer.PondPlayer {
	entry := logx.NewLogEntry(ctx)
	if len(playerIds) <= 0 {
		return nil
	}

	pipLine := redisx.GetGameCli().TxPipeline()
	for _, playerId := range playerIds {
		pipLine.HGetAll(ctx, config.FmtSpotPlayerRdsKey(playerId))
	}

	cmds, err := pipLine.Exec(ctx)
	if err != nil {
		entry.Errorf("batch get spot player info, playerIds:%v, err:%v", playerIds, err)
		return nil
	}

	playerMap := make(map[uint64]*modelPlayer.PondPlayer)
	for i, cmd := range cmds {
		playerId := playerIds[i]
		playerHashMap, err := cmd.(*redis.StringStringMapCmd).Result()
		if errors.Is(err, redis.Nil) {
			continue
		}

		playerMap[playerId] = modelPlayer.NewPlayerInfoFormRdsField(playerHashMap)
	}

	return playerMap
}