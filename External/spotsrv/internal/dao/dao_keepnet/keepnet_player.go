package daoKeepnet

import (
	"context"
	"spotsrv/internal/config"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/go-redis/redis/v8"
)

// 这里处理玩家鱼护更新时间戳 用于对超过执行时间的玩家进行自动发奖操作 zset

// AddKeepnetPlayer 添加鱼护玩家和时间到keepnet中
func AddKeepnetPlayer(ctx context.Context, playerId uint64, timestamp int64) error {
	err := redisx.GetGameCli().ZAdd(ctx, config.RDS_KEEPNET_PLAYER, &redis.Z{Score: float64(timestamp), Member: playerId}).Err()
	return err
}

// DelKeepnetPlayer 删除指定鱼护玩家的数据
func DelKeepnetPlayer(ctx context.Context, playerId uint64) error {
	err := redisx.GetGameCli().ZRem(ctx, config.RDS_KEEPNET_PLAYER, playerId).Err()
	return err
}

// BatchDelKeepnetPlayer 批量删除鱼护玩家数据
func BatchDelKeepnetPlayer(ctx context.Context, playerIds []uint64) error {
	pipLine := redisx.GetGameCli().TxPipeline()
	for _, playerId := range playerIds {
		pipLine.ZRem(ctx, config.RDS_KEEPNET_PLAYER, playerId)
	}
	_, err := pipLine.Exec(ctx)

	return err
}

// GetAllKeepnetPlayer 查询所有鱼护玩家数据 玩家id:时间戳
func GetAllKeepnetPlayer(ctx context.Context) (map[uint64]int64, error) {
	res, err := redisx.GetGameCli().ZRangeWithScores(ctx, config.RDS_KEEPNET_PLAYER, 0, -1).Result()
	if err != nil {
		return nil, err
	}
	
	retMap := make(map[uint64]int64, len(res))
	for _, z := range res {
		retMap[transform.Str2Uint64(z.Member.(string))] = int64(z.Score)
	}

	return retMap, nil
}