package proc

import (
	"context"
	"spotsrv/internal/config"
	daoPlayer "spotsrv/internal/dao/dao_player"
	"spotsrv/internal/logic/logic_durability"
	logicEvent "spotsrv/internal/logic/logic_event"
	logicExit "spotsrv/internal/logic/logic_exit"
	logicFish "spotsrv/internal/logic/logic_fish"
	logicKeepnet "spotsrv/internal/logic/logic_keepnet"
	logicRoom "spotsrv/internal/logic/logic_room"
	modelKeepnet "spotsrv/internal/model/model_keepnet"
	modelPlayer "spotsrv/internal/model/model_player"

	"spotsrv/internal/pubsub/publish"
	"spotsrv/internal/repo/record"
	rpcHook "spotsrv/internal/repo/rpc_hook"
	rpcTrip "spotsrv/internal/repo/rpc_trip"

	"sync"

	daoKeep "spotsrv/internal/dao/dao_keep"
	logicNotify "spotsrv/internal/logic/logic_notify"
	logicPond "spotsrv/internal/logic/logic_pond"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	spotPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/spot"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_model/rpc_model_stats"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"git.keepfancy.xyz/back-end/frameworks/kit/safego"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/prometheus/client_golang/prometheus"
)

func init() {
	prometheus.MustRegister(config.HookPlayerCount,
		config.HookFishCount)
}

type SpotService struct {
}

var (
	once     = &sync.Once{}
	instance *SpotService
)

func GetSpotServiceInstance() *SpotService {
	if instance != nil {
		return instance
	}

	once.Do(func() {
		instance = &SpotService{}
	})
	return instance
}

// GetSpotSceneReq 钓点场景请求
func (s *SpotService) GetSpotSceneReq(ctx context.Context, req *spotPB.GetSpotSceneReq) *spotPB.GetSpotSceneRsp {
	entry := logx.NewLogEntry(ctx)
	playerId := interceptor.GetRPCOptions(ctx).PlayerId
	rsp := &spotPB.GetSpotSceneRsp{
		Ret:    protox.DefaultResult(),
		PondId: req.GetPondId(),
		SpotId: req.GetSpotId(),
	}

	if req.GetRoomId() == "" {
		entry.Errorf("player:%d enter spot roomId is empty, req:%s", playerId, req.String())
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_BAD_PARAM)
		rsp.Ret.Desc = "room id is empty"
		return rsp
	}

	// 判断玩家是否在此房间
	roomInfo, err := rpcTrip.GetPlayerRoomInfo(ctx, playerId)
	if err != nil || roomInfo == nil {
		entry.Errorf("player:%d  enter spot req:%s, GetPlayerRoomInfo error:%v", playerId, req.String(), err)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SPOT_GET_ROOM)
		rsp.Ret.Desc = "get room info error"
		return rsp
	}

	// 玩家所在房间和请求的房间不一致
	if roomInfo.RoomId != req.GetRoomId() || req.GetPondId() != roomInfo.PondId {
		entry.Errorf("player:%d  enter spot req:%s, not equal trip:%s", playerId, req.String(), roomInfo.String())
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SPOT_NOT_IN_ROOM)
		rsp.Ret.Desc = "not in the room"
		return rsp
	}

	// 查询房间中玩家信息列表
	playerList, err := daoPlayer.GetRoomAllPlayerInfoRds(ctx, req.RoomId)

	if err != nil {
		entry.Errorf("GetSpotSceneReq, player:%d, roomId:%s, GetRoomPlayerInfoRds error:%v", playerId, req.RoomId, err)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SPOT_ROOM_PLAYER)
		rsp.Ret.Desc = "get room player error"
		return rsp
	}

	// 查询玩家钓组信息
	playerInfo, err := daoPlayer.QuerySpotPlayerInfo(ctx, playerId)
	if err != nil {
		entry.Errorf("GetSpotSceneReq, player:%d, roomId:%s, GetPlayerSpotInfo error:%v", playerId, req.RoomId, err)
		rsp.Ret.Desc = "get spot player info error"
		return rsp
	}

	if playerInfo != nil {
		rsp.RigId = playerInfo.RigId
	}

	rsp.SyncList = make([]*commonPB.FisherSpotSync, 0)
	// 组合返回结果
	for playerID := range playerList {
		rsp.SyncList = append(rsp.SyncList, &commonPB.FisherSpotSync{
			PlayerId: playerID,
		})
	}

	// 更新玩家钓场场景
	curEnergy, _ := logicPond.UpdatePlayerPondScene(ctx, playerId, req.GetRoomId(), req.GetPondId(), req.GetSpotId())

	// 广播玩家进入消息
	logicNotify.SendPlayerEnterNtf(ctx, playerId, req.GetRoomId(), req.GetPondId(), req.GetSpotId())

	// 进入房间事件发布
	publish.PublishSpotEnterSpot(ctx, playerId, req.GetPondId(), req.GetRoomId(), req.GetSpotId())

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.Energy = curEnergy

	entry.Debugf("player:%d enter spot success req:%s, rsp:%s", playerId, req.String(), rsp.String())

	return rsp
}

// SyncSpotInfoReq 同步钓点信息
func (s *SpotService) SyncSpotInfoReq(ctx context.Context, req *spotPB.SyncSpotInfoReq) *spotPB.SyncSpotInfoBroadcastNtf {
	// playerId := interceptor.GetRPCOptions(ctx).PlayerId

	// // 查询玩家所在房间id
	// playerRoom, err := rpcTrip.GetPlayerRoomId(ctx, playerId)
	// if err != nil {
	// 	entry.Errorf("SyncSpotInfoReq, player:%d, GetPlayerRoomId error:%v", playerId, err)
	// 	return nil
	// }

	// // 广播给房间玩家
	// ntf := &spotPB.SyncSpotInfoBroadcastNtf{
	// 	SyncInfo: req.SyncInfo,
	// }

	// // 广播给房间玩家
	// logicNotify.SendRoomBroadcastNtf(ctx, playerId, playerRoom, commonPB.MsgID_CMD_SYNC_SPOT_INFO_BS_NTF, ntf)

	return nil
}

// ThrowRodReq 抛竿请求
func (s *SpotService) ThrowRodReq(ctx context.Context, req *spotPB.ThrowRodReq) *spotPB.ThrowRodRsp {
	entry := logx.NewLogEntry(ctx)
	playerId := interceptor.GetRPCOptions(ctx).PlayerId

	rsp := &spotPB.ThrowRodRsp{Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL),
		PondId:   req.GetPondId(),
		RigId:    req.GetRigId(),
		HookBait: req.GetHookBait(),
	}

	if req == nil {
		entry.Errorf("player:%d, req:%v is nil", playerId, req)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_BAD_PARAM)
		return rsp
	}

	// 调用rpc接口 获取抛竿结果
	syncInfo, err := logicFish.PlayerThrowRod(ctx, playerId, req)

	if err != nil {
		entry.Errorf("player:%d throw req:%s error:%+v", playerId, req.String(), err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}

	// 数据监控(钓鱼人数):增加
	config.HookPlayerCount.WithLabelValues(transform.Int642Str(req.GetPondId())).Inc()
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.SyncControl = syncInfo

	entry.Debugf("[ThrowRodReq]:req:%s, rsp:%s", req.String(), rsp.String())

	// 异步执行扣除耐久动作，失败打印日志
	safego.Go(func() {
		logic_durability.ThrowLossDurability(ctx, playerId, req.GetRigId())
	})

	return rsp
}

// FishHookReq 中鱼请求
func (s *SpotService) FishHookReq(ctx context.Context, req *spotPB.FishHookReq) *spotPB.FishHookRsp {
	entry := logx.NewLogEntry(ctx)

	playerId := interceptor.GetRPCOptions(ctx).PlayerId
	rsp := &spotPB.FishHookRsp{
		PondId:   req.GetPondId(),
		RigId:    req.GetRigId(),
		GridInfo: req.GetGridInfo(),
		FishInfo: nil,
	}

	// rpc 请求中鱼信息
	fishInfo, nextReqTime, fakeFish, err := rpcHook.PlayerFishHook(ctx, playerId, req)
	if err != nil {
		entry.Errorf("player:%d, pondId:%d, rigId:%d, gridInfo:%v, error:%v", playerId, req.GetPondId(), req.GetRigId(), req.GetGridInfo(), err)
		return rsp
	}

	rsp.FishInfo = fishInfo
	rsp.NextReqTime = nextReqTime
	rsp.FakeFishId = fakeFish

	entry.Debugf("[FishHook]:req:%s, rsp:%s", req.String(), rsp.String())

	return rsp
}

// CatchRodReq 收杆请求
func (s *SpotService) CatchRodReq(ctx context.Context, req *spotPB.CatchRodReq) *spotPB.CatchRodRsp {
	playerId := interceptor.GetRPCOptions(ctx).PlayerId
	rsp := &spotPB.CatchRodRsp{FishResult: commonPB.FISH_RESULT_FR_NOTHING,
		PondId: req.GetPondId(),
		RigId:  req.GetRigId(),
	}
	entry := logx.NewLogEntry(ctx)

	if req == nil {
		entry.Errorf("player:%d, req:%v is nil", playerId, req)
		return rsp
	}

	// rpc 请求收杆信息
	hookBait, ret, fishInfo := rpcHook.PlayerCatchRod(ctx, playerId, req)

	// 数据监控(钓鱼人数):减少
	config.HookPlayerCount.WithLabelValues(transform.Int642Str(req.GetPondId())).Dec()

	// 耐久处理
	defer func() {
		// 中到鱼和不中鱼的耐久扣除逻辑不一样
		safego.Go(func() {
			isCatch := rsp.GetFishResult() == commonPB.FISH_RESULT_FR_TAKED || rsp.GetFishResult() == commonPB.FISH_RESULT_FR_HOOKED
			logic_durability.CatchLossDurability(ctx, playerId, hookBait, isCatch)
		})
	}()

	// 没有上鱼 直接返回
	if ret != commonPB.FISH_RESULT_FR_HOOKED && ret != commonPB.FISH_RESULT_FR_TAKED {
		rsp.FishResult = ret
		entry.Debugf("player:%d, pondId:%d, rigId:%d, ret:%s", playerId, req.GetPondId(), req.GetRigId(), ret)
		return rsp
	}

	// 补充fishInfo 中的其他信息
	fishDetail := modelKeepnet.NewFishDetailFromPb(ctx, fishInfo, req.GetFishDamagedInfo())
	fishInfo = fishDetail.FishInfo.ToProto(ctx)
	// 补充钓鱼信息
	fishDetail.BaitId = hookBait.GetBaitId()

	// 查询是否首次
	productId := interceptor.GetRPCOptions(ctx).ProductId
	fishStats, err := rpc_model_stats.GetStatsMgrInstance().GetFishStats(ctx, productId, playerId, fishInfo.GetSpecial())
	if err != nil {
		entry.Errorf("statsSrv.GetFishStats.err:%v", err)
		rsp.FishResult = commonPB.FISH_RESULT_FR_FISH_OUT
		return rsp
	}
	fishDetail.IsFirst = fishStats.Count == 0

	// 保存到redis
	if err := daoKeep.SetPlayerHookFish(ctx, playerId, fishDetail); err != nil {
		entry.Errorf("player:%d, pondId:%d, rigId:%d, fishDetail:%v, SetPlayerHookFish error:%v", playerId, req.GetPondId(), req.GetRigId(), fishDetail, err)
		rsp.FishResult = commonPB.FISH_RESULT_FR_FISH_OUT
		return rsp
	}

	// 上鱼数据监控:鱼数量
	config.HookFishCount.WithLabelValues(transform.Int642Str(fishDetail.FishInfo.FishId)).Observe(float64(1))

	// 中鱼流水
	rData := &record.HookFishRecordParam{
		Ctx:      ctx,
		PlayerId: playerId,
		FishInfo: fishDetail.FishInfo,
		Req:      req,
	}
	record.DefaultLogging.PubHookFishRecord(rData)

	rsp.FishInfo = fishDetail.ToProto(ctx)
	rsp.FishResult = ret

	// 处理玩家中鱼
	logicFish.DealPlayerHookFish(ctx, playerId, fishDetail)

	// 中鱼事件后执行逻辑处理
	logicFish.DealAfterHookFish(ctx, playerId, req, fishInfo)

	entry.Debugf("req:%s, rsp:%s", req.String(), rsp.String())

	return rsp
}

// ExitRoomReq 离开房间请求
func (s *SpotService) ExitRoomReq(ctx context.Context, req *spotPB.ExitRoomReq) *spotPB.ExitRoomRsp {
	entry := logx.NewLogEntry(ctx)
	rsp := &spotPB.ExitRoomRsp{Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL)}

	playerId := interceptor.GetRPCOptions(ctx).PlayerId

	settleInfo, err := logicExit.PlayerExitRoom(ctx, playerId)

	if err != nil {
		entry.Errorf("ExitRoomReq, player:%d, exit spot error:%v", playerId, err)
		return rsp
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.SettleInfo = settleInfo

	entry.Debugf("ExitRoomReq, player:%d, req:%s, rsp:%s success", playerId, req.String(), rsp.String())

	return rsp
}

// FishEntryOptReq 鱼入护操作请求
func (s *SpotService) FishEntryOptReq(ctx context.Context, req *spotPB.FishEntryOptReq) *spotPB.FishEntryOptRsp {
	entry := logx.NewLogEntry(ctx)
	playerId := interceptor.GetRPCOptions(ctx).PlayerId
	rsp := &spotPB.FishEntryOptRsp{Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL)}
	if req == nil {
		entry.Errorf("player:%d, req:%v is nil", playerId, req)
		return rsp
	}

	rsp.Action = req.GetAction()

	// 获取结果
	totalWeight, errCode := logicFish.FishEntryOpt(ctx, playerId, req.GetFishInstance(), req.GetAction())
	if errCode != commonPB.ErrCode_ERR_SUCCESS {
		rsp.Ret = protox.FillCodeResult(errCode)
		entry.Errorf("player:%d, req:%v, PlayerFishEntryOpt error:%d", playerId, req, errCode)
		return rsp
	}

	// 鱼入护广播
	if req.GetAction() == commonPB.FISH_ENTRY_OPT_TYPE_FEOT_KEEP {
		ntfMsg := spotPB.PlayerFishEntryBroadcastNtf{
			PlayerId:      playerId,
			FishWeight:    totalWeight,
			KeepnetWeight: logicFish.KEEPNEI_WEIGHT_LIMIT,
		}
		logicNotify.SendPlayerRoomBroadcastNtf(ctx, playerId, commonPB.MsgID_CMD_PLAYER_FISH_ENTRY_BS_NTF, &ntfMsg)
	}

	entry.Debugf("player:%d, req:%v, PlayerFishEntryOpt success, totalWeight:%d", playerId, req, totalWeight)

	rsp.FishWeight = totalWeight
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	return rsp
}

// FishKeepnetOptReq 鱼护操作请求
func (s *SpotService) FishKeepnetOptReq(ctx context.Context, req *spotPB.FishKeepnetOptReq) *spotPB.FishKeepnetOptRsp {
	playerId := interceptor.GetRPCOptions(ctx).PlayerId
	entry := logx.NewLogEntry(ctx)

	rsp := &spotPB.FishKeepnetOptRsp{Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL)}
	if req == nil {
		entry.Errorf("player:%d, req:%v is nil", playerId, req)
		return rsp
	}

	rsp.Action = req.GetAction()
	rsp.FishInstance = req.GetFishInstance()

	// 逻辑接口
	fishWeight, err := logicKeepnet.PlayerFishKeepnetOpt(ctx, playerId, req.GetFishInstance(), req.GetAction())
	if err != nil {
		entry.Errorf("player:%d, req:%v, PlayerFishKeepnetOpt error:%v", playerId, req, err)
		return rsp
	}

	entry.Debugf("player:%d, req:%v, PlayerFishKeepnetOpt success, fishWeight:%d", playerId, req, fishWeight)

	rsp.FishWeight = fishWeight
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	// 广播给其他玩家
	ntfMsg := spotPB.PlayerFishEntryBroadcastNtf{
		PlayerId:      playerId,
		FishWeight:    fishWeight,
		KeepnetWeight: logicFish.KEEPNEI_WEIGHT_LIMIT,
	}

	logicNotify.SendPlayerRoomBroadcastNtf(ctx, playerId, commonPB.MsgID_CMD_PLAYER_FISH_ENTRY_BS_NTF, &ntfMsg)

	return rsp
}

// KeepnetFishInfoReq 鱼护中鱼信息请求
func (s *SpotService) KeepnetFishInfoReq(ctx context.Context, req *spotPB.KeepnetFishInfoReq) *spotPB.KeepnetFishInfoRsp {
	playerId := interceptor.GetRPCOptions(ctx).PlayerId
	entry := logx.NewLogEntry(ctx)
	rsp := &spotPB.KeepnetFishInfoRsp{}
	if req == nil {
		entry.Errorf("player:%d, req:%v is nil", playerId, req)
	}

	// 逻辑接口
	fishList, err := logicKeepnet.GetPlayerKeepnetFishInfo(ctx, playerId)
	if err != nil {
		entry.Errorf("player:%d, req:%v, GetPlayerKeepnetFishInfo error:%v", playerId, req, err)
		return rsp
	}

	for _, fishDetail := range fishList {
		if fishDetail.OptType == commonPB.FISH_ENTRY_OPT_TYPE_FEOT_KEEP {
			rsp.FishInfo = append(rsp.FishInfo, fishDetail.ToProto(ctx))
		}
	}

	entry.Debugf("player:%d, req:%v, GetPlayerKeepnetFishInfo success, rsp:%v", playerId, req, rsp)

	return rsp
}

// GetRoomAllPlayerInfoReq 获取房间所有玩家信息请求
func (s *SpotService) GetRoomAllPlayerInfoReq(ctx context.Context, req *spotPB.GetRoomAllPlayerInfoReq) *spotPB.GetRoomAllPlayerInfoRsp {
	rsp := &spotPB.GetRoomAllPlayerInfoRsp{}
	entry := logx.NewLogEntry(ctx)

	// 逻辑接口
	playerId := interceptor.GetRPCOptions(ctx).PlayerId
	PlayerList, err := logicRoom.GetRoomAllPlayerInfo(ctx, playerId)
	if err != nil {
		entry.Errorf("player:%d, req:%v, GetPlayerKeepnetFishInfo error:%v", playerId, req, err)
		return rsp
	}

	rsp.PlayerInfo = PlayerList
	entry.Debugf("player:%d, req:%v, GetRoomAllPlayerInfo success, rsp:%v", playerId, req, rsp)

	return rsp
}

// ChooseSpotReq 选择钓点请求
func (s *SpotService) ChooseSpotReq(ctx context.Context, req *spotPB.ChooseSpotReq) *spotPB.ChooseSpotRsp {
	rsp := &spotPB.ChooseSpotRsp{
		Ret:    protox.DefaultResult(),
		SpotId: req.GetSpotId(),
	}
	entry := logx.NewLogEntry(ctx)

	playerId := interceptor.GetRPCOptions(ctx).PlayerId

	errCode := logicPond.PlayerChooseSpot(ctx, playerId, req.GetSpotId())
	if errCode != commonPB.ErrCode_ERR_SUCCESS {
		rsp.Ret = protox.FillCodeResult(errCode, "choose spot error")
		entry.Errorf("player:%d, choose spot spotId:%d, choose spot errCode:%d", playerId, req.GetSpotId(), errCode)
		return rsp
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Debugf("player:%d, choose spot success, req:%s, rsp:%s", playerId, req.String(), rsp.String())

	return rsp
}

// FishBattleReq 搏鱼请求
func (s *SpotService) FishBattleReq(ctx context.Context, req *spotPB.FishBattleReq) *spotPB.FishBattleRsp {
	rsp := &spotPB.FishBattleRsp{
		Ret:    protox.DefaultResult(),
		Result: req.GetResult(),
		RigId:  req.GetRigId(),
	}
	entry := logx.NewLogEntry(ctx)
	playerId := interceptor.GetRPCOptions(ctx).PlayerId

	// 扣除搏鱼体力
	logicFish.DeductPlayerFightFishEnergy(ctx, playerId)

	rpcRsp, err := rpcHook.PlayerBattleFish(ctx, playerId, req.GetRigId(), req.GetResult())
	if err != nil {
		entry.Errorf("player:%d, req:%v, PlayerBattleFish error:%v", playerId, req, err)
		rsp.Ret = rpcRsp.Ret
		return rsp
	}

	rsp.FishInfo = rpcRsp.GetFishInfo()
	rsp.Ret = rpcRsp.Ret

	entry.Debugf("player:%d battle fish req:%v, rsp:%v", playerId, req, rsp)

	return rsp
}

// SwitchRodRigReq 切换鱼竿请求
func (s *SpotService) SwitchRodRigReq(ctx context.Context, req *spotPB.SwitchRodRigReq) *spotPB.SwitchRodRigRsp {
	rsp := &spotPB.SwitchRodRigRsp{
		RigId: req.GetRigId(),
		Ret:   protox.DefaultResult(),
	}
	entry := logx.NewLogEntry(ctx)

	playerId := interceptor.GetRPCOptions(ctx).PlayerId

	playerInfo := &modelPlayer.PondPlayer{
		RigId: req.GetRigId(),
	}
	err := daoPlayer.UpdateSpotPlayerRds(ctx, playerId, playerInfo.BuildRedisHMSetMap([]string{"rig_id"}))
	if err != nil {
		entry.Errorf("update player:%d rig id error:%v", playerId, err)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL, err.Error())
		return rsp
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Debugf("player:%d switch rod rig req:%+v success", playerId, req)

	return rsp
}

// PlayerEnergyCostReq 玩家消耗体力请求
func (s *SpotService) PlayerEnergyCostReq(ctx context.Context, req *spotPB.PlayerEnergyCostReq) *spotPB.PlayerEnergyCostRsp {
	rsp := &spotPB.PlayerEnergyCostRsp{
		Ret: protox.DefaultResult(),
	}
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("PlayerEnergyCostReq:%+v", req)
	playerId := interceptor.GetRPCOptions(ctx).PlayerId

	err := logicFish.DeductPlayerEnergy(ctx, playerId, req.GetEnergy())
	if err != nil {
		entry.Errorf("update error:%+v", err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.Energy = req.GetEnergy()

	entry.Debugf("PlayerEnergyCostReq success")
	return rsp
}

// FishEventReq 钓鱼事件请求
func (s *SpotService) FishEventReq(ctx context.Context, req *spotPB.FishingEventReq) *spotPB.FishingEventRsp {
	entry := logx.NewLogEntry(ctx)

	playerId := interceptor.GetRPCOptions(ctx).PlayerId
	rsp := &spotPB.FishingEventRsp{
		Ret:       protox.DefaultResult(),
		EventInfo: req.GetEventInfo(),
	}

	err := logicEvent.PlayerFishingEvent(ctx, playerId, req.GetNeedBroadcast(), req.GetEventInfo())
	if err != nil {
		entry.Errorf("player:%d, req:%v, PlayerFishingEvent error:%v", playerId, req, err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Debugf("player:%d, req:%+v, PlayerFishingEvent rsp:%+v", playerId, req, rsp)

	return rsp
}

// HookStartReq 中鱼开始请求
func (s *SpotService) HookStartReq(ctx context.Context, req *spotPB.HookStartReq) *spotPB.HookStartRsp {
	entry := logx.NewLogEntry(ctx)
	playerId := interceptor.GetRPCOptions(ctx).PlayerId

	rsp := &spotPB.HookStartRsp{Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL)}
	hookBait := req.GetHookBait()

	if req == nil || req.GetHookHabit() == nil || hookBait == nil || hookBait.GetBaitId() <= 0 {
		entry.Errorf("player:%d, req:%v param is unvalid", playerId, req)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_BAD_PARAM)
		return rsp
	}

	// 调用rpc接口 获取开始中鱼结果
	syncInfo, err := rpcHook.RpcHookStart(ctx, playerId, req)

	if err != nil || syncInfo == nil {
		entry.Errorf("player:%d throw req:%s, syncInfo:%+v, error:%+v", playerId, req.String(), syncInfo, err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.SyncControl = syncInfo

	entry.Debugf("[HookStartReq]:req:%s, rsp:%s", req.String(), rsp.String())

	return rsp
}

// KillLineReq 切线请求
func (s *SpotService) KillLineReq(ctx context.Context, req *spotPB.KillLineReq) *spotPB.KillLineRsp {
	entry := logx.NewLogEntry(ctx)
	playerId := interceptor.GetRPCOptions(ctx).PlayerId

	rsp := &spotPB.KillLineRsp{
		Ret: protox.DefaultResult(),
	}

	// 切线
	err := logic_durability.KillLineLossDurability(ctx, playerId, req.GetHookBait())
	if err != nil {
		entry.Infof("player:%d, req:%v, KillLineLossDurability fail:%v", playerId, req, err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}

	entry.Debugf("player:%d, req:%v, KillLineReq success", playerId, req)
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	return rsp
}
