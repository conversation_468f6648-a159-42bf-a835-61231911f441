package proc

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/kit/transport"
)

// RegClientMsgHandler 注册客户端消息Handler
func RegClientMsgHandler() {
	regSpotHandler()
}

func regSpotHandler() {
	// TODO 协议注册接口
	handler := &SpotHandler{}
	// 请求钓点情景
	transport.Handler(int(commonPB.MsgID_CMD_GET_SPOT_SCENE_REQ), handler.GetSpotSceneReq)

	// 同步情景数据
	transport.Handler(int(commonPB.MsgID_CMD_SYNC_SPOT_INFO_REQ), handler.SyncSpotInfoReq)

	// 抛竿请求
	transport.Handler(int(commonPB.MsgID_CMD_THROW_ROD_REQ), handler.ThrowRodReq)

	// 中鱼请求
	transport.Handler(int(commonPB.MsgID_CMD_FISH_HOOK_REQ), handler.FishHookReq)

	// 收竿请求
	transport.Handler(int(commonPB.MsgID_CMD_CATCH_ROD_REQ), handler.CatchRodReq)

	// 退出房间
	transport.Handler(int(commonPB.MsgID_CMD_EXIT_ROOM_REQ), handler.ExitRoomReq)

	// 鱼入护操作请求
	transport.Handler(int(commonPB.MsgID_CMD_FISH_ENTRY_OPT_REQ), handler.FishEntryOptReq)

	// 鱼护操作请求
	transport.Handler(int(commonPB.MsgID_CMD_FISH_KEEPNET_OPT_REQ), handler.FishKeepnetOptReq)

	// 鱼护详细信息查询
	transport.Handler(int(commonPB.MsgID_CMD_KEEPNET_FISH_INFO_REQ), handler.KeepnetFishInfoReq)

	// 房间玩家信息请求
	transport.Handler(int(commonPB.MsgID_CMD_GET_ROOM_ALL_PLAYER_INFO_REQ), handler.GetRoomAllPlayerInfoReq)

	// 选择钓点请求
	transport.Handler(int(commonPB.MsgID_CMD_CHOOSE_SPOT_REQ), handler.ChooseSpotReq)

	// 搏鱼请求
	transport.Handler(int(commonPB.MsgID_CMD_FISH_BATTLE_FISH_REQ), handler.FishBattleReq)
	
	// 切换竿组
	transport.Handler(int(commonPB.MsgID_CMD_SWITCH_ROD_RIG_REQ), handler.SwitchRodRigReq)

	// 用户体力消耗 
	transport.Handler(int(commonPB.MsgID_CMD_PLAYER_ENERGY_COST_REQ), handler.PlayerEnergyCostReq)

	// 钓鱼事件请求
	transport.Handler(int(commonPB.MsgID_CMD_SPOT_FISHING_EVENT_REQ), handler.FishEventReq)

	// 中鱼开始请求
	transport.Handler(int(commonPB.MsgID_CMD_SPOT_HOOK_START_REQ), handler.HookStartReq)

	// 切线请求
	transport.Handler(int(commonPB.MsgID_CMD_SPOT_KILL_LINE_REQ), handler.KillLineReq)
}
