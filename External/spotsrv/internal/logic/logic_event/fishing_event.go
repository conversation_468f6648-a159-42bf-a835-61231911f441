package logicEvent

import (
	"context"

	logicNotify "spotsrv/internal/logic/logic_notify"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
)

// PlayerFishingEvent 钓鱼事件广播通知
func PlayerFishingEvent(ctx context.Context, playerId uint64, needBs bool, eventInfo *commonPB.FishingEventInfo) error {
	entry := logx.NewLogEntry(ctx)

	if !needBs {
		return nil
	}

	if eventInfo == nil {
		entry.Error("eventInfo is nil")
		return protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "eventInfo is nil")
	}

	// 参数校验
	eventType := eventInfo.GetEventType()
	if eventType != commonPB.FISHING_EVENT_TYPE_FET_CONGRATULATION{
		entry.Error("eventType is not broadcast event type")
		return protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "eventType is not broadcast event type")
	}

	err := logicNotify.SendFishingEventBsNtf(ctx, playerId, eventInfo)

	return err
}