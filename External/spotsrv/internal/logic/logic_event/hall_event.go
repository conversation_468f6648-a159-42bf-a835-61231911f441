package logicEvent

import (
	"context"
	"fmt"
	"sort"

	daoPlayer "spotsrv/internal/dao/dao_player"
	logicNotify "spotsrv/internal/logic/logic_notify"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

// RecvPlayerInfoChangeEvent 大厅事件通知 玩家等级变化等
func RecvPlayerInfoChangeEvent(ctx context.Context, playerId uint64, roomInfo *commonPB.RoomInfo, eventList []*commonPB.PondEventChangeInfo) error {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 || len(eventList) == 0 || roomInfo == nil {
		return fmt.Errorf("param error")
	}

	// 查询玩家钓场数据
	playerInfo, err := daoPlayer.QuerySpotPlayerInfo(ctx, playerId)
	if playerInfo == nil || err != nil {
		entry.Errorf("query player:%d from room info:%s pond info error:%v", playerId, roomInfo.String(), err)
		return fmt.Errorf("daoPlayer.QuerySpotPlayerInfo error")
	}

	updFieldMap := make(map[string]bool)
	// 增加的体力值
	var addEnergy int32
	for _, event := range eventList {
		switch event.GetEventId() {
		case commonPB.POND_EVENT_CHANGE_TYPE_PECV_EX_LEVEL:
			playerInfo.ExpLevel = int32(event.GetAfterNum())
			updFieldMap["exp_level"] = true
			energy, err := CalcExpLevelUpAddEnergy(ctx, event.GetBeforeNum(), event.GetAfterNum())
			if energy > 0 && err == nil {
				updFieldMap["energy"] = true
				addEnergy += energy
			}
		case commonPB.POND_EVENT_CHANGE_TYPE_PECV_ENERGY_CHANGE:
			addEnergy += int32(event.GetChangeNum())
			updFieldMap["energy"] = true
		}

		entry.Debugf("player:%d, room:%s, cur energy:%d, event:%s, add energy:%d", playerId, roomInfo.String(), playerInfo.Energy, event.String(), addEnergy)
	}

	updFieldList := make([]string, 0)
	for field, _ := range updFieldMap {
		updFieldList = append(updFieldList, field)
	}

	if addEnergy > 0 {
		playerInfo.Energy += addEnergy
		maxEnergy, err := GetMaxEnergyForExpLevel(ctx, int64(playerInfo.ExpLevel))
		// 超过上限 等于上限
		if err == nil && playerInfo.Energy > maxEnergy {
			playerInfo.Energy = maxEnergy
		}

		// 通知客户端体力更新
		logicNotify.SendPlayerEnergyChange(ctx, playerId, playerInfo.Energy)

		entry.Debugf("player:%d, room:%s, update player info:%v", playerId, roomInfo.String(), *playerInfo)
	}

	// 没有变化 返回
	if len(updFieldList) <= 0 {
		return nil
	}

	// 更新玩家信息
	err = daoPlayer.UpdateSpotPlayerRds(ctx, playerId, playerInfo.BuildRedisHMSetMap(updFieldList))

	return err
}

// CalcExpLevelUpAddEnergy 计算升级增加的体力值
func CalcExpLevelUpAddEnergy(ctx context.Context, beforeLevel int64, afterLevel int64) (int32, error) {
	entry := logx.NewLogEntry(ctx)
	if afterLevel <= beforeLevel {
		return 0, nil
	}

	// 查询等级经验配置
	expLevelConf := cmodel.GetAllRoleLevel(consulconfig.WithGrpcCtx(ctx))
	if expLevelConf == nil {
		entry.Errorf("calc exp level up add energy error: no role level config")
		return 0, fmt.Errorf("no role level config")
	}

	// 将expLevelConf的map结构按key从小到大排序
	expLevelList := make([]*cmodel.RoleLevel, 0, len(expLevelConf))
	for _, levelItem := range expLevelConf {
		expLevelList = append(expLevelList, levelItem)
	}

	sort.Slice(expLevelList, func(i, j int) bool {
		return expLevelList[i].Id < expLevelList[j].Id
	})

	beforeLevelConf := expLevelConf[beforeLevel]
	afterLevelConf := expLevelConf[afterLevel]

	if beforeLevelConf == nil || afterLevelConf == nil {
		entry.Errorf("calc exp level up add energy error: no role level config")
		return 0, fmt.Errorf("no role level config")
	}

	// 增加的体力值
	// if beforeLevelConf.EnergyNum < afterLevelConf.EnergyNum {
	// 	return afterLevelConf.EnergyNum - beforeLevelConf.EnergyNum, nil
	// }

	return afterLevelConf.EnergyNum, nil
}

// GetMaxEnergyForExpLevel 查询当前等级的最大体力值
func GetMaxEnergyForExpLevel(ctx context.Context, expLevel int64) (int32, error) {
	entry := logx.NewLogEntry(ctx)
	// 等级体力配置
	roleLevelConf := cmodel.GetRoleLevel(int64(expLevel), consulconfig.WithGrpcCtx(ctx))
	if roleLevelConf == nil {
		entry.Errorf("get exp level:%d config error", expLevel)
		return 0, fmt.Errorf("get exp level:%d config error", expLevel)
	}

	return roleLevelConf.EnergyNum, nil
}
