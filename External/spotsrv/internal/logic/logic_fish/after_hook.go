package logicFish

import (
	"context"
	"spotsrv/internal/pubsub/publish"
	"spotsrv/internal/repo/rpc_world"

	logicNotify "spotsrv/internal/logic/logic_notify"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	spotPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/spot"
	"git.keepfancy.xyz/back-end/frameworks/kit/safego"
)

// DealAfterHookFish 上鱼之后的处理
func DealAfterHookFish(ctx context.Context, playerId uint64, req *spotPB.CatchRodReq, fishInfo *commonPB.FishInfo) error {
	weatherInfo := rpc_world.RpcGetWeather(ctx, req.GetPondId())
	var weather int64 = 0
	if weatherInfo != nil {
		weather = int64(weatherInfo.GetData().GetSky())
	}

	// 收杆事件
	publish.PublishCatchRod(ctx, playerId, fishInfo, req.GetPondId(), weather, 0)
	// 中鱼播报
	logicNotify.SendMsgBroadcastByFish(ctx, playerId, commonPB.MSG_BROADCAST_EVENT_MBE_CATCH_FISH, req.PondId, fishInfo, weather)

	// 异步处理玩家钓鱼事件广播
	eventInfo := &commonPB.FishingEventInfo{
		EventType: commonPB.FISHING_EVENT_TYPE_FET_HOOK_FISH,
		PlayerId:  playerId,
		IntData: map[int32]int64{
			int32(commonPB.EVENT_INT_KEY_EIK_FISH_ID):     fishInfo.GetFishId(),
			int32(commonPB.EVENT_INT_KEY_EIK_FISH_WEIGHT): int64(fishInfo.GetWeight()),
		},
	}

	safego.Go(func() {
		logicNotify.SendFishingEventBsNtf(ctx, playerId, eventInfo)
	})

	return nil
}
