package logicFish

import (
	"context"
	"errors"
	"fmt"
	daoPlayer "spotsrv/internal/dao/dao_player"
	logicNotify "spotsrv/internal/logic/logic_notify"
	modelKeepnet "spotsrv/internal/model/model_keepnet"
	rpcHook "spotsrv/internal/repo/rpc_hook"

	spotPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/spot"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/item_kit"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// PlayerThrowRod 玩家抛竿
func PlayerThrowRod(ctx context.Context, playerId uint64, req *spotPB.ThrowRodReq) (*commonPB.FishSyncControl, error) {
	entry := logx.NewLogEntry(ctx)
	// 先判断玩家体力
	playerInfo, err := daoPlayer.QuerySpotPlayerInfo(ctx, playerId)
	if err != nil || playerInfo == nil || req.GetHookHabit() == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM)
	}

	// 判断玩家是否有体力
	if playerInfo.Energy <= 0 {
		entry.Warnf("player:%d throw rod error, energy:%d", playerId, playerInfo.Energy)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_SPOT_NOT_ENOUGH_POWER)
	}

	// 扣除玩家体力
	energyConstConf := cmodel.GetRoleConst(consulconfig.WithGrpcCtx(ctx))
	if energyConstConf == nil {
		entry.Errorf("get role const config error")
	} else {
		if energyConstConf.ThrowRodCostHp > 0 {
			playerInfo.Energy -= energyConstConf.ThrowRodCostHp
			if playerInfo.Energy < 0 {
				playerInfo.Energy = 0
			}

			// 更新体力值
			daoPlayer.UpdateSpotPlayerRds(ctx, playerId, playerInfo.BuildRedisHMSetMap([]string{"energy"}))

			// 通知客户端
			logicNotify.SendPlayerEnergyChange(ctx, playerId, playerInfo.Energy)
		}

		entry.Debugf("player:%d throw rod, energy:%d", playerId, playerInfo.Energy)
	}

	// 先扣体力 再执行抛竿逻辑
	syncInfo, err := rpcHook.PlayerThrowRod(ctx, playerId, req)
	if err != nil {
		return nil, err
	}

	return syncInfo, nil
}

// DealPlayerHookFish 处理玩家中鱼逻辑
func DealPlayerHookFish(ctx context.Context, playerId uint64, fishDetail *modelKeepnet.FishDetailInfo) error {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 || fishDetail == nil {
		entry.Errorf("deal player:%d hook fish error, param error, fish info:%v", playerId, *fishDetail)
		return errors.New("param error")
	}

	// 发送经验奖励
	SendPlayerTravelExpReward(ctx, playerId, fishDetail)

	return nil
}

// SendPlayerTravelExpReward 发送玩家旅游经验奖励
func SendPlayerTravelExpReward(ctx context.Context, playerId uint64, fishDetail *modelKeepnet.FishDetailInfo) error {
	entry := logx.NewLogEntry(ctx)
	if fishDetail == nil {
		entry.Errorf("send player:%d travel exp reward, fishDetail is nil", playerId)
		return fmt.Errorf("param error")
	}

	if fishDetail.FishInfo == nil || fishDetail.FishInfo.Exp <= 0 {
		entry.Errorf("send player:%d travel exp reward, fishInfo:%v exp not find", playerId, *fishDetail)
		return nil
	}

	itemList := []*commonPB.ItemBase{
		{
			ItemId:    int64(commonPB.ITEM_TYPE_IT_CURRENCY_EXP),
			ItemCount: int64(fishDetail.FishInfo.Exp),
		},
	}

	// 通知大厅发奖
	_, err := item_kit.SendReward(ctx, playerId, itemList, commonPB.ITEM_SOURCE_TYPE_IST_FISH_CATCH_ADD, true)

	entry.Debugf("send player travel exp reward, playerId:%d, fishDetail:%v, ret:%v", playerId, *fishDetail, err)

	return err
}

// DeductPlayerFightFishEnergy 扣除玩家搏鱼体力
func DeductPlayerFightFishEnergy(ctx context.Context, playerId uint64) {
	// 先判断玩家体力
	entry := logx.NewLogEntry(ctx)

	energyConstConf := cmodel.GetRoleConst(consulconfig.WithGrpcCtx(ctx))
	if energyConstConf == nil {
		entry.Errorf("get role const config error")
		return
	}

	if energyConstConf.CatchFishCostHp > 0 {
		DeductPlayerEnergy(ctx, playerId, energyConstConf.CatchFishCostHp)
	}

	entry.Debugf("player:%d catch fish  cost:%d", playerId, energyConstConf.CatchFishCostHp)
}

// DeductPlayerEnergy 扣除玩家体力
func DeductPlayerEnergy(ctx context.Context, playerId uint64, change int32) error {
	entry := logx.NewLogEntry(ctx)
	playerInfo, err := daoPlayer.QuerySpotPlayerInfo(ctx, playerId)
	if err != nil || playerInfo == nil {
		entry.Errorf("get player:%d spot info error:%v", playerId, err)
		return err
	}

	playerInfo.Energy -= change
	if playerInfo.Energy < 0 {
		playerInfo.Energy = 0
	}

	// 更新体力值
	err = daoPlayer.UpdateSpotPlayerRds(ctx, playerId, playerInfo.BuildRedisHMSetMap([]string{"energy"}))
	if err != nil {
		return err
	}
	// 通知客户端
	logicNotify.SendPlayerEnergyChange(ctx, playerId, playerInfo.Energy)
	return nil
}
