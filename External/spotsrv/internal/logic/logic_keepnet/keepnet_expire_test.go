package logicKeepnet

import (
	daoKeepnet "spotsrv/internal/dao/dao_keepnet"
	modelKeepnet "spotsrv/internal/model/model_keepnet"
	test_init "spotsrv/internal/test"

	"testing"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)


func TestRunCheckKeepnetExpire(t *testing.T) {
	test_init.Init()

	// 先写入数据
	ctx := test_init.NewCtxWithPlayerId(1)

	fishDetail := modelKeepnet.NewFishDetailFromPb(ctx, &commonPB.FishInfo{
		FishId:   101031003,
		Length:   10,
		Weight:   100,
		Exp:      100,
	},
	&commonPB.FishDamagedInfo{
		FishDamagedValue: 80,
		FishDamagedLv: 1,
	})
	
	for i := 1; i < 10; i++ {
		daoKeepnet.AddPlayerKeepnetFish(ctx, uint64(1638), fishDetail)
	}

	RunCheckKeepnetExpire()
}
