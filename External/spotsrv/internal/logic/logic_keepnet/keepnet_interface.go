package logicKeepnet

import (
	"context"
	"fmt"
	daoKeepnet "spotsrv/internal/dao/dao_keepnet"
	modelKeepnet "spotsrv/internal/model/model_keepnet"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
)

type KeepnetOptInterface interface {
	PlayerKeepnetOpt(ctx context.Context, playerId uint64, fishInstance string) (interface{}, error)
}

func KeepnetOpt(ctx context.Context, playerId uint64, fishInstance string, optType commonPB.FISH_KEEPNET_OPT_TYPE) (interface{}, error) {
	var keepnetOpt KeepnetOptInterface
	switch optType {
	case commonPB.FISH_KEEPNET_OPT_TYPE_FKOT_DISCARD:
		keepnetOpt = new(KeepnetDiscard)
	case commonPB.FISH_KEEPNET_OPT_TYPE_FKOT_FREEZE:
		keepnetOpt = new(KeepnetFreeze)
	default:
		keepnetOpt = nil
	}

	if keepnetOpt != nil {
		return keepnetOpt.PlayerKeepnetOpt(ctx, playerId, fishInstance)
	}

	return nil, fmt.Errorf("invalid opt type: %d", optType)
}

// 丢弃操作
type KeepnetDiscard struct {
}

func (k *KeepnetDiscard) PlayerKeepnetOpt(ctx context.Context, playerId uint64, fishInstance string) (interface{}, error) {
	entry := logx.NewLogEntry(ctx)
	// 查询玩家鱼护信息
	mapFishKeepnet, err := daoKeepnet.GetPlayerKeepnetFish(ctx, playerId)
	if err != nil || len(mapFishKeepnet) == 0 {
		entry.Errorf("player discard keepnet fish get keepnet info error, playerId:%d, fishInstance:%s, err:%v", playerId, fishInstance, err)
		return nil, err
	}

	// 初始化成鱼护信息
	fishKeepnet := modelKeepnet.NewFishKeepnetFromRedisHash(ctx, mapFishKeepnet)
	err = fishKeepnet.DiscardFish(fishInstance)
	if err != nil {
		entry.Errorf("player discard keepnet fish error, playerId:%d, fishInstance:%s, err:%v", playerId, fishInstance, err)
		return nil, err
	}

	// 更新鱼护信息
	err = daoKeepnet.AddPlayerKeepnetFish(ctx, playerId, fishKeepnet.FishList[fishInstance])
	if err != nil {
		entry.Errorf("player discard keepnet fish redis error, playerId:%d, fishInstance:%s, err:%v", playerId, fishInstance, err)
		return nil, err
	}

	return fishKeepnet.TotalWeight, nil
}

// 冷冻操作
type KeepnetFreeze struct {
}

func (k *KeepnetFreeze) PlayerKeepnetOpt(ctx context.Context, playerId uint64, fishInstance string) (interface{}, error) {

	return int32(0), nil
}
