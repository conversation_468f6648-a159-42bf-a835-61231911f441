package logicKeepnet

import (
	"context"
	"spotsrv/internal/config"
	modelKeepnet "spotsrv/internal/model/model_keepnet"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"

	daoKeepnet "spotsrv/internal/dao/dao_keepnet"
	rpcMsg "spotsrv/internal/repo/rpc_msg"

	"git.keepfancy.xyz/back-end/frameworks/kit/dlm"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"github.com/robfig/cron"
	"github.com/sirupsen/logrus"
)


func Init() {
	c := cron.New()
	
	// 每天三点执行一次
	c.AddFunc("0 0 3 * * *", RunCheckKeepnetExpire)

	// 启动定时任务
	c.Start()
}

// RunCheckKeepnetExpire 检测Keepnet过期时间，并更新数据库中的过期时间信息
func RunCheckKeepnetExpire() {
	IsGet, unlock := dlm.DefaultLockMgr.OptimisticLockKey(config.RDS_LOCK_KEEPNET_CORN, 300)
	if !IsGet {
		return
	}
	defer unlock()

	// 初始化上下文
	ctx := interceptor.NewRpcClientCtx(interceptor.WithProductId(int32(commonPB.PRODUCT_ID_PID_FISHER)), interceptor.WithChannelType(int32(commonPB.CHANNEL_TYPE_CT_GOOGLE)))

	logrus.Info("RunCheckKeepnetExpire start...")

	// 2.查询所有玩家的Keepnet过期时间
	playerKeepnetMap, err := daoKeepnet.GetAllKeepnetPlayer(ctx)
	if err != nil || len(playerKeepnetMap) == 0 {
		return
	}

	// 3.过滤超过14天的玩家
	playerList := make([]uint64, 0)
	now := timex.Now().Unix()
	for playerId, updTime := range playerKeepnetMap {
		if updTime + config.KEEPNET_EXPIRE_TIME <= now {
			playerList = append(playerList, playerId)
		}
	}

	if len(playerList) == 0 {
		return
	}
 
	// 4.发奖
	successPlayerList := BatchSendPlayerKeepnetAward(ctx, playerList)

	// 5.删除返奖成功的玩家的Keepnet信息
	daoKeepnet.BatchDelPlayerKeepnet(ctx, successPlayerList)

	logrus.Infof("RunCheckKeepnetExpire playerList:%+v end...", successPlayerList)
}

// BatchSendPlayerKeepnetAward 批量发奖 返回发奖成功玩家id
func BatchSendPlayerKeepnetAward(ctx context.Context, playerIdList []uint64) []uint64 {
	// 批量查询玩家Keepnet信息
	playerKeepnetMap, err := daoKeepnet.GetPlayerKeepnetFishBatch(ctx, playerIdList)
	if err != nil || len(playerKeepnetMap) == 0 {
		return nil
	}

	entry := logx.NewLogEntry(ctx)

	// 遍历玩家Keepnet信息，发送邮件奖励
	successPlayerList := make([]uint64, 0)

	for playerId, keepnetInfo := range playerKeepnetMap {
		err = SendPlayerKeepnetAwardMail(ctx, playerId, keepnetInfo.FishList) 
		if err != nil {
			entry.Errorf("send player:%d keepnet award mail error:%v", playerId, err)
			continue
		}
		// 保存发奖成功的玩家id
		successPlayerList = append(successPlayerList, playerId)
	}

	return successPlayerList
}

// SendPlayerKeepnetAwardMail 发送玩家Keepnet邮件奖励
func SendPlayerKeepnetAwardMail(ctx context.Context, playerId uint64, keepnetInfo map[string]*modelKeepnet.FishDetailInfo) error {
	
	itemList := make([]*commonPB.ItemBase, 0)
	itemMap := make(map[int64]*commonPB.ItemBase, 0)
	
	// 奖励加入玩家资产
	for _, fishDetail := range keepnetInfo {
		// 跳过放生标记的鱼
		if fishDetail.OptType == commonPB.FISH_ENTRY_OPT_TYPE_FEOT_RELEASE {
			continue
		}
		oneList := fishDetail.ToItemBase(ctx)
		if len(oneList) <= 0 {
			continue
		}

		// 合并奖励
		for _, one := range oneList {
			if _, ok := itemMap[one.ItemId]; ok {
				itemMap[one.ItemId].ItemCount += one.ItemCount
			} else {
				itemMap[one.ItemId] = one
			}
		}
	}

	for _, item := range itemMap {
		itemList = append(itemList, item)
	}

	// 发奖邮件
	mailInfo := SendKeepnetAwardMail(ctx, playerId, itemList)
	err := rpcMsg.SendMail(ctx, playerId, mailInfo)

	return err
}

// SendKeepnetAwardMail 给玩家发送Keepnet邮件奖励
func SendKeepnetAwardMail(ctx context.Context, playerId uint64, itemList []*commonPB.ItemBase) *commonPB.MailAssembly {
	if len(itemList) == 0 {
		return nil
	}

	extend := make(map[int32]int64, 0)

	mail := &commonPB.MailAssembly{
		TemplateId:  config.EXPIRE_EMAIL_TEMPLATE,
		Extend     : extend,
		MailType   : commonPB.MAIL_TYPE_MT_ORDINARY,
		CreateTime : timex.Now().Unix(),
		ExpiresTime: timex.Now().Unix() + 7*86400,
		Rewards    : &commonPB.ItemBaseList{
			ItemList: itemList,
		},
	}

	return mail
}