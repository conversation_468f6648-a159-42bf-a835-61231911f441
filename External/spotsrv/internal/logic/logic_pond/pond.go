package logicPond

import (
	"context"
	daoPlayer "spotsrv/internal/dao/dao_player"
	logicEvent "spotsrv/internal/logic/logic_event"
	modelPlayer "spotsrv/internal/model/model_player"
	rpcTrip "spotsrv/internal/repo/rpc_trip"

	"git.keepfancy.xyz/back-end/frameworks/lib/timex"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_hall"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

// PlayerChooseSpot 玩家选择钓点
func PlayerChooseSpot(ctx context.Context, playerId uint64, spotId int32) commonPB.ErrCode {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 || spotId <= 0 {
		entry.Errorf("player:%d, spotId:%d error", playerId, spotId)
		return commonPB.ErrCode_ERR_BAD_PARAM
	}

	roomInfo, err := rpcTrip.GetPlayerRoomInfo(ctx, playerId)
	if err != nil || roomInfo == nil {
		entry.Errorf("player:%d choose spot:%d GetPlayerRoomInfo  roomInfo:%v, error:%v", playerId, spotId, roomInfo, err)
		return commonPB.ErrCode_ERR_SYSTEM_MISTAKE
	}

	// 查询玩家信息
	playerInfo, err := daoPlayer.QuerySpotPlayerInfo(ctx, playerId)
	if playerInfo == nil || err != nil {
		entry.Errorf("player:%d, query game info error:%v", playerId, err)
		return commonPB.ErrCode_ERR_SYSTEM_MISTAKE
	}

	// 判断钓点是否可开启
	// 查询钓场配置
	pondConf := cmodel.GetFishPondList(int64(roomInfo.PondId), consulconfig.WithGrpcCtx(ctx))
	if pondConf == nil {
		entry.Errorf("player:%d choose spot:%d pond:%d conf not exist", playerId, spotId, roomInfo.PondId)
		return commonPB.ErrCode_ERR_CONF_ERROR
	}

	// 钓点关闭
	if !transform.Int32SliceContain(pondConf.OpenSpot, spotId) {
		entry.Errorf("player:%d choose spotId:%d not open", playerId, spotId)
		return commonPB.ErrCode_ERR_HALL_SPOT_CLOSE
	}

	// 更新玩家信息
	playerInfo.SpotId = spotId
	playerInfo.PondId = roomInfo.PondId
	daoPlayer.UpdateSpotPlayerRds(ctx, playerId, playerInfo.BuildRedisHMSetMap([]string{"spot_id"}))

	// 更新上次游戏信息
	daoPlayer.UpdatePlayerLastGameSpotId(ctx, playerId, spotId)

	entry.Debugf("player:%d choose spotId:%d success", playerId, spotId)

	return commonPB.ErrCode_ERR_SUCCESS
}

// UpdatePlayerPondScene 更新玩家钓场情景 返回当前体力值
func UpdatePlayerPondScene(ctx context.Context, playerId uint64, roomId string, pondId int64, spotId int32) (int32, error) {
	entry := logx.NewLogEntry(ctx)
	// 查询玩家钓场信息
	playerInfo, err := daoPlayer.QuerySpotPlayerInfo(ctx, playerId)
	if err != nil {
		entry.Errorf("player:%d, update player pond scene error:%v", playerId, err)
		return 0, err
	}

	if playerInfo == nil {
		// 首次进入 添加玩家体力等级等信息
		return PlayerFirstEnterSpot(ctx, playerId, roomId, pondId, spotId)
	}

	// 有变化
	if playerInfo.PondId != pondId || playerInfo.SpotId != spotId {

		playerInfo.PondId = pondId
		playerInfo.SpotId = spotId
		// 更新redis
		daoPlayer.UpdateSpotPlayerRds(ctx, playerId, playerInfo.BuildRedisHMSetMap([]string{"pond_id", "spot_id"}))
	}

	entry.Debugf("player:%d update pond:%d, spot:%d success", playerId, pondId, spotId)

	return playerInfo.Energy, nil
}

// PlayerFirstEnterSpot 玩家首次进入钓点 返回当前体力值
func PlayerFirstEnterSpot(ctx context.Context, playerId uint64, roomId string, pondId int64, spotId int32) (int32, error) {
	playerInfo := &modelPlayer.PondPlayer{
		PondId:    pondId,
		SpotId:    spotId,
		EnterTime: timex.Now().Unix(),
	}
	entry := logx.NewLogEntry(ctx)
	// 查询玩家基本数据
	playerBaseInfo, err := crpc_hall.RpcQueryPlayerBaseInfo(ctx, playerId)
	if err != nil {
		entry.Errorf("query player:%d base info error:%v", playerId, err)
		return 0, err
	}

	// 保存玩家体力等级等信息
	playerInfo.ExpLevel = playerBaseInfo.ExpLevel

	// 当前等级的体力
	maxEnergy, err := logicEvent.GetMaxEnergyForExpLevel(ctx, int64(playerInfo.ExpLevel))

	if err != nil {
		entry.Errorf("player:%d, get max energy for exp level error:%v", playerId, err)
		return 0, err
	}

	playerInfo.Energy = maxEnergy

	// 更新redis
	err = daoPlayer.UpdateSpotPlayerRds(ctx, playerId, playerInfo.ToRedisHashField())

	entry.Debugf("player:%d first enter pond:%d, room:%s, spot:%d, success, player info:%v", playerId, pondId, roomId, pondId, *playerInfo)

	return playerInfo.Energy, err
}
