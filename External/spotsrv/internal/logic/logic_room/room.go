package logicRoom

import (
	"context"
	rpcTrip "spotsrv/internal/repo/rpc_trip"

	daoKeepnet "spotsrv/internal/dao/dao_keepnet"
	daoPlayer "spotsrv/internal/dao/dao_player"
	rpcUser "spotsrv/internal/repo/rpc_user"

	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
)

// GetRoomAllPlayerInfo 查询房间所有玩家鱼护信息
func GetRoomAllPlayerInfo(ctx context.Context, playerId uint64) ([]*commonPB.FisheryPlayerInfo, error) {
	entry := logx.NewLogEntry(ctx)
	// 查询玩家房间id
	playerRoomId, err := rpcTrip.GetPlayerRoomId(ctx, playerId)
	if err != nil || playerRoomId == "" {
		entry.Errorf("query player room field, playerId:%d, roomId:%s, error:%+v", playerId, playerRoomId, err)
		return nil, err
	}

	// 查询当前房间所有玩家信息
	playerList, err := daoPlayer.GetRoomAllPlayerInfoRds(ctx, playerRoomId)
	if err != nil || len(playerList) == 0 {
		entry.Errorf("query room all player info field, playerId:%d, roomId:%s, error:%+v", playerId, playerRoomId, err)
		return nil, err
	}

	playerIdList := make([]uint64, 0, len(playerList))
	for playerID, playerInfo := range playerList {
		// 无效玩家 不再查询玩家鱼护信息 
		if !playerInfo.IsValid() { 
			continue
		}
		
		playerIdList = append(playerIdList, playerID)
	}

	if len(playerIdList) == 0 {
		entry.Warnf("query room all player info empty, playerId:%d, roomId:%s, playerList:%+v valid player empty...", playerId, playerRoomId, playerList)
		return nil, nil
	}

	// 查询玩家全量信息
	richInfoMap, _ := rpcUser.RpcGetPlayerInfoBatch(ctx, interceptor.GetRPCOptions(ctx).ProductId, playerIdList)

	// 批量查询玩家渔护信息
	playerKeepnetMap, err := daoKeepnet.GetPlayerKeepnetFishBatch(ctx, playerIdList)
	if err != nil {
		entry.Errorf("query player keepnet field, playerId:%d, error:%+v", playerId, err)
		return nil, err
	}

	// 组合数据
	fisheryPlayerList := make([]*commonPB.FisheryPlayerInfo, 0)
	for playerID, playerInfo := range playerList {
		// 非鱼塘玩家不处理 这种情况是玩家虽然在房间 但是不是手动进入的 TODO 这里后续需要处理玩家长时间不登录时的鱼护信息
		if !playerInfo.IsValid() { 
			continue
		}

		fisheryPlayer := &commonPB.FisheryPlayerInfo{
			UserInfo: &commonPB.BriefUserInfo{
				PlayerId: playerID,
			},
			
			SpotId:     playerInfo.SpotId,
			ExpLevel:   playerInfo.ExpLevel,
			Energy:     playerInfo.Energy,
		}

		// 玩家基本信息
		if richInfo, ok := richInfoMap[playerID]; ok {
			fisheryPlayer.UserInfo = richInfo.GetBriefUserInfo()
		}

		// 有鱼护信息 赋值鱼总重量
		if playerKeepnet, ok := playerKeepnetMap[playerID]; ok {
			fisheryPlayer.FishWeight = playerKeepnet.TotalWeight
		}

		fisheryPlayerList = append(fisheryPlayerList, fisheryPlayer)
	}

	entry.Debugf("get room all player info, playerId:%d, fisheryPlayerList:%+v", playerId, fisheryPlayerList)

	return fisheryPlayerList, nil
}
