package rpc_msg

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	msgrpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/msgrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_msg"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"

	"github.com/spf13/viper"
)

// SendBroadcastByTemp 按模板广播 rpc到msg服
func SendBroadcastByTemp(ctx context.Context, bcInfo *commonPB.MsgBroadcastDetailInfo) (*commonPB.Result, commonPB.ErrCode) {
	if bcInfo.GetBcType() == 0 || bcInfo.GetTemplateId() == 0 {
		return nil, commonPB.ErrCode_ERR_BAD_PARAM
	}

	// 请求msg服
	opts := interceptor.GetRPCOptions(ctx)

	rpcReq := &msgrpc.SendMsgBroadcastReq{
		ProductId: commonPB.PRODUCT_ID(opts.ProductId),
		Channel:   commonPB.CHANNEL_TYPE(opts.ChannelType),
		Detail:    bcInfo,
	}

	msgRpcCli := crpc_msg.GetMsgRpcInstance().GetMsgRpcClient()

	rpcRsp, err := msgRpcCli.SendMsgBroadcast(ctx, rpcReq)
	if err != nil || rpcRsp == nil {
		return nil, commonPB.ErrCode_ERR_OPERATION
	}

	return rpcRsp.GetRet(), commonPB.ErrCode_ERR_SUCCESS
}

// SendMail 发送邮件
func SendMail(ctx context.Context, playerId uint64, mail *commonPB.MailAssembly) error {
	if mail == nil {
		return protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "mail is nil")
	}
	
	cli := crpc_msg.GetMsgRpcInstance().GetMsgRpcClient()
	if cli == nil {
		return protox.CodeError(commonPB.ErrCode_ERR_OPERATION, "rpc client fail")
	}

	req := &msgrpc.SendMailReq{
		Sender:    viper.GetString(dict.ConfigRpcServerName),
		PlayerIds: []uint64{playerId},
		ProductId: commonPB.PRODUCT_ID(interceptor.GetRPCOptions(ctx).ProductId),
		Channel:   commonPB.CHANNEL_TYPE(interceptor.GetRPCOptions(ctx).ChannelType),
		Assembly:  mail,
	}

	_, errRet := cli.SendMail(ctx, req)
	if errRet != nil {
		return errRet
	}
	
	return nil
}