package record

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/recordx"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

// RHookFish 中鱼流水
type RHookFish struct {
	PlayerId     uint64 // 玩家id
	PondId       int64  // 钓场id
	BaitId       int64  // 鱼饵id
	FishId       int64  // 鱼id
	FishLength   int32  // 鱼长度(cm)
	FishWeight   int32  // 鱼重量(g)
	FishExp      int32  // 鱼经验
}

func (r *RHookFish) GetTableName() string {
	return "r_hook_fish"
}

func (r *RHookFish) Format() string {
	return recordx.MarshalWithLine(
		transform.Uint642Str(r.PlayerId),
		transform.Int642Str(r.PondId),
		transform.Int642Str(int64(r.BaitId)),
		transform.Int642Str(int64(r.FishId)),
		transform.Int2Str(int(r.<PERSON>)),
		transform.Int2Str(int(r.<PERSON>Weight)),
		transform.Int2Str(int(r.<PERSON>Exp)),
	)
}