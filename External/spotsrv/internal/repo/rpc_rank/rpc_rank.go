package rpc_rank

import (
	"context"
	modelKeepnet "spotsrv/internal/model/model_keepnet"
	modelPlayer "spotsrv/internal/model/model_player"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_rank"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	rankRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/rankrpc"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"git.keepfancy.xyz/back-end/frameworks/kit/safego"
)

// 对排行榜提交离场结算数据
func SubmitTripSettle(ctx context.Context, pondPlayer *modelPlayer.PondPlayer, keepnet map[string]*modelKeepnet.FishDetailInfo) {
	if len(keepnet) == 0 {
		return
	}
	opts := interceptor.GetRPCOptions(ctx)
	req := &rankRpc.SubmitTripSettleReq{
		PlayerId: opts.PlayerId,
		PondId:   pondPlayer.PondId,
	}
	var valFish *modelKeepnet.FishDetailInfo
	var weightFish *modelKeepnet.FishDetailInfo
	for _, fish := range keepnet {
		// 跳过放生标记的鱼
		if fish.OptType == commonPB.FISH_ENTRY_OPT_TYPE_FEOT_RELEASE {
			continue
		}
		if valFish == nil {
			valFish = fish
		} else if fish.FishInfo.AwardNum > valFish.FishInfo.AwardNum {
			valFish = fish
		}

		if weightFish == nil {
			weightFish = fish
		} else if fish.FishInfo.Weight > weightFish.FishInfo.Weight {
			weightFish = fish
		}
	}

	if valFish == nil && weightFish == nil {
		return
	}

	if valFish != nil {
		req.ValFish = valFish.ToProto(ctx)
	}

	if weightFish != nil {
		req.WeightFish = weightFish.ToProto(ctx)
	}

	safego.Go(
		func() {
			doSubmitTripSettle(ctx, req)
		})
}

func doSubmitTripSettle(ctx context.Context, rpcReq *rankRpc.SubmitTripSettleReq) {
	entry := logx.NewLogEntry(ctx)

	rpcCli := crpc_rank.GetRankRpcInstance().GetRankRpcClient()
	rpcRsp, err := rpcCli.SubmitTripSettle(ctx, rpcReq)
	if err != nil {
		entry.Warnf("submit trip settle failed:%+v", err)
		return
	}
	if rpcRsp.Ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		entry.Warnf("submit trip settle failed:%+v", rpcRsp.Ret)
		return
	}
}
