# server
rpc_server_name: spot
rpc_port: 12201

# 端口号
tcp_port: 22201
http_port: 32201

# 日志相关
log_level: trace
log_write: true
log_dir: ../../logs/spot
log_json: false

redis_addr: 192.168.1.58:6379
redis_passwd: 8888

redis_list:
  player:
    addr:  192.168.1.58:6379
    passwd: 8888

  game:
    addr: 192.168.1.58:6379
    passwd: 8888

  lock:
    addr: 192.168.1.58:6379
    passwd: 8888


mysql_list:
  player:
    user: root
    addr: 192.168.1.58:3306
    passwd: fancydb2024#
    db: playerdb
  

consul_addr: 192.168.1.58:8500

nsqd_addr: 192.168.1.58:4150
nsqd_http_addr: 192.168.1.58:4151
nsqlookupd_addrs:
  - 192.168.1.58:4161

rpc_server_tags: normal
# 使用的hook服 1:老的中鱼服 2:新中鱼服
rpc_hook_server: 2

jwt:
  timeout: 876010
  secret: fancygame

kafka-producer:
  brokers: ["192.168.1.58:9092"]
  timeout: 10