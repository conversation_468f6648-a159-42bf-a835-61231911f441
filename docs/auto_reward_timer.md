# 自动领奖定时任务实现文档

## 概述

本文档描述了 activitysrv 服务中实现的自动领奖定时任务，该任务每小时执行一次，为"上上个周期"的玩家自动处理奖励领取。

## 功能特性

### 核心功能
1. **定时执行**: 每小时自动执行一次
2. **活动遍历**: 获取所有活动列表并逐一处理
3. **周期计算**: 准确计算"上上个周期"（当前周期-2）
4. **玩家处理**: 遍历目标周期的所有玩家并执行自动领奖
5. **异常处理**: 完善的错误处理和日志记录
6. **数据清理**: 自动删除空的 Redis Set

### 技术特点
- **分布式锁**: 防止多实例重复执行
- **批量处理**: 高效的玩家数据获取和处理
- **幂等性**: 支持重复执行而不产生副作用
- **容错性**: 单个活动或玩家处理失败不影响整体流程

## 实现架构

### 文件结构
```
internal/timer/
├── timer.go              # 定时器初始化和管理
└── auto_reward_timer.go   # 自动领奖任务实现
```

### 核心组件

#### 1. 定时器管理 (timer.go)
- 使用 `github.com/robfig/cron` 库
- Cron 表达式: `"0 0 * * * *"` (每小时执行)
- 集成到服务启动流程

#### 2. 自动领奖任务 (auto_reward_timer.go)
- `RunAutoRewardTask()`: 主任务入口
- `processActivityAutoReward()`: 单个活动处理
- `processPlayerAutoReward()`: 单个玩家处理
- `getAllPlayersInCycle()`: 批量获取玩家数据
- `deleteEmptyPlayerSet()`: 清理空 Set

## 业务流程

### 主流程
1. **获取分布式锁**: 防止多实例重复执行
2. **获取活动列表**: 调用 `cmodel.GetAllActivity()`
3. **遍历活动**: 对每个活动执行自动领奖处理
4. **统计结果**: 记录处理成功和失败的活动数量

### 单个活动处理流程
1. **获取当前周期**: 从 Redis 获取活动的当前周期信息
2. **计算目标周期**: 计算"上上个周期" (当前周期ID - 2)
3. **获取玩家列表**: 从 Redis Set 获取目标周期的所有玩家
4. **处理空 Set**: 如果 Set 为空则删除该 Set
5. **遍历玩家**: 为每个玩家执行自动领奖逻辑

### 单个玩家处理流程
1. **创建领奖请求**: 构造 `ClaimActivityRewardReq`
2. **调用领奖逻辑**: 复用现有的 `ClaimReward` 方法
3. **处理结果**: 区分"无奖励可领取"和真正的错误
4. **清理记录**: 成功处理后从 Redis Set 中移除玩家

## 技术实现

### Redis Set 操作
- **Key 格式**: `activity_players:{activity_id}:{cycle_id}`
- **批量获取**: 使用 `SRANDMEMBER` 分批获取玩家数据
- **数据清理**: 使用 `DEL` 命令删除空 Set
- **玩家移除**: 使用 `SREM` 命令移除已处理玩家

### 分布式锁
- **锁 Key**: `lock:timer:auto_reward`
- **锁时间**: 300 秒 (5分钟)
- **实现**: 使用 `dlm.DefaultLockMgr.OptimisticLockKey`

### 错误处理策略
- **活动级错误**: 记录错误但继续处理其他活动
- **玩家级错误**: 记录错误但继续处理其他玩家
- **无奖励情况**: 不视为错误，正常清理玩家记录
- **Redis 连接错误**: 任务终止，等待下次执行

## 配置和部署

### 依赖配置
- **Redis**: 需要配置 Activity Redis 连接
- **Consul**: 需要配置活动配置中心连接
- **分布式锁**: 需要配置锁管理器

### 启动集成
在 `cmd/main.go` 的 `Init()` 方法中添加：
```go
// 初始化定时器
timer.Init()
```

### 监控和日志
- **启动日志**: 定时器初始化成功/失败
- **执行日志**: 每次任务执行的统计信息
- **错误日志**: 详细的错误信息和上下文
- **调试日志**: 详细的处理过程信息

## 运维指南

### 监控指标
- 任务执行频率 (每小时一次)
- 处理的活动数量
- 处理的玩家数量
- 错误率和失败原因

### 故障排查
1. **任务未执行**: 检查定时器是否正常启动
2. **分布式锁获取失败**: 检查是否有其他实例在执行
3. **Redis 连接失败**: 检查 Redis 配置和网络连接
4. **活动配置获取失败**: 检查 Consul 配置和连接

### 性能优化
- **批量大小**: 默认 1000，可根据实际情况调整
- **并发处理**: 当前为串行处理，可考虑并发优化
- **内存使用**: 大量玩家时注意内存使用情况

## 测试

### 单元测试
- 定时器初始化测试
- 任务执行基本功能测试
- 错误处理测试

### 集成测试
需要完整的 Redis 和 Consul 环境：
```bash
go test ./test/timer_test.go -v
```

## 扩展性

### 支持的扩展
1. **多种活动类型**: 通过活动配置自动支持
2. **不同周期策略**: 可调整目标周期计算逻辑
3. **自定义执行频率**: 修改 Cron 表达式
4. **批量大小调整**: 修改 `batchSize` 常量

### 未来优化方向
1. **并发处理**: 支持多个活动并发处理
2. **动态配置**: 支持运行时调整执行频率
3. **指标监控**: 集成 Prometheus 指标
4. **告警机制**: 集成告警系统

## 总结

该自动领奖定时任务实现了完整的业务需求，具有以下特点：

1. **可靠性**: 分布式锁保证单实例执行，完善的错误处理
2. **高效性**: 批量处理和复用现有逻辑，减少重复开发
3. **可维护性**: 清晰的代码结构和完整的日志记录
4. **可扩展性**: 支持多种活动类型和配置驱动

该实现严格按照需求文档执行，参考了 spotsrv 的定时任务模式，确保了代码的一致性和可维护性。
