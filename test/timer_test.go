package test

import (
	"activitysrv/internal/logic/logic_activity"
	"activitysrv/internal/timer"
	"testing"
	"time"
)

// TestTimerInit 测试定时器初始化
func TestTimerInit(t *testing.T) {
	// 测试定时器初始化不会panic
	defer func() {
		if r := recover(); r != nil {
			t.<PERSON><PERSON>("Timer initialization panicked: %v", r)
		}
	}()

	timer.Init()

	// 等待一小段时间确保定时器启动
	time.Sleep(100 * time.Millisecond)

	t.Log("Timer initialized successfully")
}

// TestAutoRewardTaskExecution 测试自动领奖任务执行
func TestAutoRewardTaskExecution(t *testing.T) {
	// 注意：这个测试需要Redis连接和活动配置才能完整运行
	// 在没有完整环境的情况下，我们测试函数是否能正确处理错误情况
	defer func() {
		if r := recover(); r != nil {
			// 在没有Redis的情况下，预期会有panic，这是正常的
			t.Logf("Auto reward task panicked as expected (no Redis): %v", r)
		}
	}()

	// 直接调用任务函数进行测试
	logic_activity.RunAutoRewardTask()

	t.Log("Auto reward task executed")
}
