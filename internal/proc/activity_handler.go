package proc

import (
	"context"
	activityPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/activity"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/transport"
	intranetGrpc "git.keepfancy.xyz/back-end/frameworks/kit/transport/gaterpc"
)

type ActivityHandler struct {
}

// GetActivityProgress 获取活动进度
func (s *ActivityHandler) GetActivityProgress(ctx context.Context, header *intranetGrpc.Header, req *activityPB.GetActivityProgressReq) *transport.ResponseMsg {
	rsp := GetActivityServiceInstance().GetActivityProgress(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_GET_ACTIVITY_PROGRESS_RSP, rsp)
}

// ClaimActivityReward 领取活动奖励
func (s *ActivityHandler) ClaimActivityReward(ctx context.Context, header *intranetGrpc.Header, req *activityPB.ClaimActivityRewardReq) *transport.ResponseMsg {
	rsp := GetActivityServiceInstance().ClaimActivityReward(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_CLAIM_ACTIVITY_REWARD_RSP, rsp)
}
