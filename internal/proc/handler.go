package proc

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/kit/transport"
)

// RegClientMsgHandler 注册客户端消息Handler
func RegClientMsgHandler() {

	handler := &ActivityHandler{}
	// 获取活动进度
	transport.Handler(int(commonPB.MsgID_CMD_GET_ACTIVITY_PROGRESS_REQ), handler.GetActivityProgress)
	// 领取活动奖励
	transport.Handler(int(commonPB.MsgID_CMD_CLAIM_ACTIVITY_REWARD_REQ), handler.ClaimActivityReward)
}
