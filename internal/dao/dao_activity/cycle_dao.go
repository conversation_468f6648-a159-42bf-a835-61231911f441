package dao_activity

import (
	"activitysrv/config"
	"activitysrv/internal/model"
	"context"
	"errors"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/go-redis/redis/v8"
)

// GetCurrentCycle 获取当前活动周期
func GetCurrentCycle(ctx context.Context, activityId int64) (*model.ActivityCycle, error) {
	key := config.ActivityCycleKey(activityId)

	hashMap, err := redisx.GetActivityCli().HGetAllWithNil(ctx, key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			// 当前周期不存在，需要创建
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get current cycle: %w", err)
	}

	cycle := &model.ActivityCycle{}
	err = transform.Map2Struct(hashMap, cycle)

	return cycle, err
}

// SaveCurrentCycle 保存当前周期到Redis
func SaveCurrentCycle(ctx context.Context, activityId int64, cycle *model.ActivityCycle) error {
	key := config.ActivityCycleKey(activityId)
	redisCli := redisx.GetActivityCli()

	hash := make(map[string]any)
	err := transform.Struct2Map(cycle, hash)
	if err != nil {
		return err
	}

	ttl := config.CalculateActivityTTL(cycle)

	pipe := redisCli.Pipeline()
	pipe.HMSet(ctx, key, hash)
	pipe.Expire(ctx, key, ttl)

	_, err = pipe.Exec(ctx)
	return err
}
