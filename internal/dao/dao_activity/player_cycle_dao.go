package dao_activity

import (
	"activitysrv/config"
	"activitysrv/internal/model"
	"context"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"strconv"
)

// AddPlayerToCycle 添加玩家到活动周期记录
// 使用 SADD 命令，将玩家ID添加到 Set 中
func AddPlayerToCycle(ctx context.Context, activityId int64, cycleId int32, playerId uint64) error {
	entry := logx.NewLogEntry(ctx)
	key := config.ActivityPlayersKey(activityId, cycleId)
	redisCli := redisx.GetActivityCli()

	member := strconv.FormatUint(playerId, 10)

	// 使用 SADD 添加玩家到 Set
	_, err := redisCli.SAdd(ctx, key, member).Result()

	if err != nil {
		entry.Errorf("failed to add player to cycle: activityId=%d, cycleId=%d, playerId=%d, err=%v",
			activityId, cycleId, playerId, err)
		return fmt.Errorf("failed to add player to cycle: %w", err)
	}

	entry.Debugf("player added to cycle: activityId=%d, cycleId=%d, playerId=%d",
		activityId, cycleId, playerId)

	return nil
}

// RemovePlayerFromCycle 从活动周期记录中移除玩家
// 在玩家成功领取奖励后调用
func RemovePlayerFromCycle(ctx context.Context, activityId int64, cycleId int32, playerId uint64) error {
	entry := logx.NewLogEntry(ctx)
	key := config.ActivityPlayersKey(activityId, cycleId)
	redisCli := redisx.GetActivityCli()

	member := strconv.FormatUint(playerId, 10)

	result, err := redisCli.SRem(ctx, key, member).Result()
	if err != nil {
		entry.Errorf("failed to remove player from cycle: activityId=%d, cycleId=%d, playerId=%d, err=%v",
			activityId, cycleId, playerId, err)
		return fmt.Errorf("failed to remove player from cycle: %w", err)
	}

	if result == 1 {
		entry.Debugf("player removed from cycle: activityId=%d, cycleId=%d, playerId=%d",
			activityId, cycleId, playerId)
	} else {
		entry.Debugf("player not found in cycle: activityId=%d, cycleId=%d, playerId=%d",
			activityId, cycleId, playerId)
	}

	return nil
}

// SetPlayerCycleTTL 为活动周期玩家记录设置TTL
// 使用与活动周期数据相同的TTL策略
func SetPlayerCycleTTL(ctx context.Context, activityId int64, cycleId int32, cycle *model.ActivityCycle) error {
	entry := logx.NewLogEntry(ctx)
	key := config.ActivityPlayersKey(activityId, cycleId)
	redisCli := redisx.GetActivityCli()

	ttl := config.CalculateActivityTTL(cycle)

	err := redisCli.Expire(ctx, key, ttl).Err()
	if err != nil {
		entry.Errorf("failed to set TTL for player cycle: activityId=%d, cycleId=%d, ttl=%v, err=%v",
			activityId, cycleId, ttl, err)
		return fmt.Errorf("failed to set TTL for player cycle: %w", err)
	}

	entry.Debugf("TTL set for player cycle: activityId=%d, cycleId=%d, ttl=%v",
		activityId, cycleId, ttl)

	return nil
}

// GetPlayersInCycle 随机获取活动周期内的玩家列表
func GetPlayersInCycle(ctx context.Context, activityId int64, cycleId int32, count int64) ([]uint64, error) {
	entry := logx.NewLogEntry(ctx)
	key := config.ActivityPlayersKey(activityId, cycleId)
	redisCli := redisx.GetActivityCli()

	// 使用 SRANDMEMBER 随机获取指定数量的成员
	members, err := redisCli.SRandMemberN(ctx, key, count).Result()
	if err != nil {
		entry.Errorf("failed to get random players in cycle: activityId=%d, cycleId=%d, count=%d, err=%v",
			activityId, cycleId, count, err)
		return nil, fmt.Errorf("failed to get random players in cycle: %w", err)
	}

	playerIds := make([]uint64, 0, len(members))
	for _, member := range members {
		if playerId, err := strconv.ParseUint(member, 10, 64); err == nil {
			playerIds = append(playerIds, playerId)
		} else {
			entry.Warnf("invalid player ID in cycle: %s", member)
		}
	}

	return playerIds, nil
}

// DeletePlayerCycleSet 删除玩家周期Set
func DeletePlayerCycleSet(ctx context.Context, key string) error {
	entry := logx.NewLogEntry(ctx)
	redisCli := redisx.GetActivityCli()

	err := redisCli.Del(ctx, key).Err()
	if err != nil {
		entry.Errorf("failed to delete player cycle set: key=%s, err=%v", key, err)
		return fmt.Errorf("failed to delete player cycle set: %w", err)
	}

	entry.Debugf("player cycle set deleted: key=%s", key)
	return nil
}

// GetPlayerCycleCount 获取活动周期内的玩家总数
func GetPlayerCycleCount(ctx context.Context, activityId int64, cycleId int32) (int64, error) {
	entry := logx.NewLogEntry(ctx)
	key := config.ActivityPlayersKey(activityId, cycleId)
	redisCli := redisx.GetActivityCli()

	count, err := redisCli.SCard(ctx, key).Result()
	if err != nil {
		entry.Errorf("failed to get player count in cycle: activityId=%d, cycleId=%d, err=%v",
			activityId, cycleId, err)
		return 0, fmt.Errorf("failed to get player count in cycle: %w", err)
	}

	return count, nil
}
