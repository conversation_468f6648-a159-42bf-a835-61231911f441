package dao_activity

import (
	"activitysrv/config"
	"activitysrv/internal/model"
	"context"
	"errors"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"github.com/go-redis/redis/v8"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
)

// GetUserData 获取用户活动数据
func GetUserData(ctx context.Context, activityId int64, userId uint64, cycleId int32) (*model.UserActivityData, error) {
	key := config.PlayerActivityDataKey(activityId, userId, cycleId)
	redisCli := redisx.GetActivityCli()

	// 使用Get
	data, err := redisCli.Get(ctx, key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return model.NewUserActivityData(), nil
		}
		return nil, fmt.Errorf("failed to get user activity data: %w", err)
	}

	userData := &model.UserActivityData{}
	err = userData.FromJSONString(data)
	if err != nil {
		return nil, fmt.Errorf("failed to parse user activity data: %w", err)
	}

	return userData, nil
}

// SaveUserData 保存用户活动数据
func SaveUserData(ctx context.Context, playerId uint64, activityCfg *cmodel.Activity, cycle *model.ActivityCycle, userData *model.UserActivityData) error {
	key := config.PlayerActivityDataKey(activityCfg.Id, playerId, cycle.CycleId)

	data, err := userData.ToJSON()
	if err != nil {
		return fmt.Errorf("failed to serialize user activity data: %w", err)
	}

	ttl := config.CalculateActivityTTL(cycle)
	err = redisx.GetActivityCli().SetEX(ctx, key, data, ttl).Err()

	return err
}
